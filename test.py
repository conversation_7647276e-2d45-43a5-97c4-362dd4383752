import pandas as pd
df = pd.read_csv('best_10_utr5_ngf.csv')
d = df.to_dict('records')
# print(df.head())
print(d[-1])

# print(d[0:len(d)-1])

from openai import OpenAI

# 连接本地 Ollama 服务
# client = OpenAI(base_url="http://10.200.4.137:11434/v1", api_key="ollama")
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

# 配置 ChatOpenAI 指向本地 Ollama
llm = ChatOpenAI(
    model="gemma3",   # 确保你已经 ollama pull llama2
    base_url="http://10.200.4.137:11434/v1",
    api_key="ollama",
)

# 构建一个简单的 Prompt
prompt = ChatPromptTemplate.from_messages([
    ("system", "你是一个乐于助人的AI助手"),
    ("user", "{input}"),
])

# 组合成链
chain = prompt | llm

if __name__ == "__main__":
    result = chain.invoke({"input": "请写一首关于秋天的七言律诗"})
    print("AI 回复：\n", result.content)

# def chat():
#     response = client.chat.completions.create(
#         model="gemma3",  # 确保已经下载：ollama pull llama2
#         messages=[
#             {"role": "system", "content": "你是一个乐于助人的AI助手。"},
#             {"role": "user", "content": "给我写一首七言绝句，主题是冬天天"},
#         ],
#     )
#     print("AI 回复：\n", response.choices[0].message.content)

# if __name__ == "__main__":
#     chat()
