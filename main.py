#!/usr/bin/env python3
"""
RNA Sequence Analysis Orchestrator
主协调器：整合序列解析 Agent 和 RAG Agent，生成分析报告
"""

import sys;
import os;
from typing import Dict, List, Any, Optional;
from datetime import datetime;
from pydantic import BaseModel, Field;

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)));

from agents.sequence_parser import SequenceParserAgent;
from agents.rag_agent import RAGAgent;
from utils.local_functions import LocalSequenceFunction;
from config.api_config import validate_config;


class AnalysisReport(BaseModel):
    """分析报告"""
    input_text: str = Field(description='输入的自然语言文本');
    extracted_sequence: Optional[str] = Field(description='提取的 RNA 序列');
    sequence_type: Optional[str] = Field(description='序列类型');
    local_function_result: Optional[Dict[str, Any]] = Field(description='本地函数处理结果');
    knowledge_search_results: Optional[Dict[str, Any]] = Field(description='知识库搜索结果');
    final_analysis: str = Field(description='最终分析报告');
    timestamp: datetime = Field(description='分析时间');
    confidence_score: float = Field(description='整体置信度 (0-1)');


class RNAAnalysisOrchestrator:
    """RNA 分析协调器"""
    
    def __init__(self, knowledge_base_path: str = './data'):
        """初始化协调器"""
        # 验证配置
        if not validate_config():
            raise ValueError('API 配置不完整，请检查 .env 文件');

        # 获取配置
        from config.api_config import get_langchain_config;
        self.config = get_langchain_config();

        # 初始化各个组件
        print('正在初始化 RNA 分析系统...');
        self.sequence_parser = SequenceParserAgent();
        print('✓ 序列解析 Agent 已初始化');

        self.rag_agent = RAGAgent(knowledge_base_path);
        print('✓ RAG Agent 已初始化');

        self.local_functions = LocalSequenceFunction();
        print('✓ 本地函数模块已初始化');

        print('✓ RNA 分析系统初始化完成\n');
    
    def analyze_sequence(self, input_text: str, local_function_name: str = 'example_function') -> AnalysisReport:
        """分析 RNA 序列"""
        print(f'开始分析输入文本: {input_text[:100]}...');
        start_time = datetime.now();
        
        try:
            # 第一步：解析序列
            print('\n步骤 1: 解析自然语言并提取序列...');
            sequence_result = self.sequence_parser.parse_sequence(input_text);
            print(sequence_result, "sequence_result")
            if not sequence_result.extracted_sequence:
                raise ValueError(f'未能提取有效序列: {sequence_result.error_message}');
            
            print(f'✓ 提取到序列: {sequence_result.extracted_sequence}');
            print(f'✓ 序列类型: {sequence_result.sequence_type}');
            print(f'✓ 置信度: {sequence_result.confidence:.2f}');
            
            # 第二步：本地函数处理
            print('\n步骤 2: 执行本地函数处理...');
            local_result = self.local_functions.execute_function(
                local_function_name, 
                sequence_result.extracted_sequence
            );
            
            if not local_result.success:
                raise ValueError(f'本地函数执行失败: {local_result.error_message}');
            
            print(f'✓ 本地函数 "{local_function_name}" 执行成功');
            print(f'✓ 执行时间: {local_result.execution_time:.4f}s');
            
            # 第三步：知识库搜索
            print('\n步骤 3: 搜索相关知识库...');
            search_queries = self._generate_search_queries(sequence_result, local_result);
            
            knowledge_results = {};
            for query_name, query in search_queries.items():
                print(f'  搜索: {query}');
                result = self.rag_agent.search_knowledge(query, top_k=3);
                knowledge_results[query_name] = {
                    'query': query,
                    'documents_found': len(result.documents),
                    'summary': result.summary,
                    'documents': [
                        {
                            'title': doc.metadata.get('title', '无标题'),
                            'content_preview': doc.page_content[:200] + '...' if len(doc.page_content) > 200 else doc.page_content,
                            'source': doc.metadata.get('source', '未知'),
                        }
                        for doc in result.documents
                    ],
                };
            
            print(f'✓ 完成 {len(search_queries)} 个知识库搜索');
            
            # 第四步：生成最终报告
            print('\n步骤 4: 生成最终分析报告...');
            final_analysis = self._generate_final_analysis(
                sequence_result, local_result, knowledge_results
            );
            
            # 计算整体置信度
            confidence_score = self._calculate_overall_confidence(
                sequence_result, local_result, knowledge_results
            );
            
            execution_time = (datetime.now() - start_time).total_seconds();
            print(f'✓ 分析完成，总耗时: {execution_time:.2f}s');
            
            return AnalysisReport(
                input_text=input_text,
                extracted_sequence=sequence_result.extracted_sequence,
                sequence_type=sequence_result.sequence_type,
                local_function_result={
                    'function_name': local_result.function_name,
                    'success': local_result.success,
                    'output_sequence': local_result.output_sequence,
                    'execution_time': local_result.execution_time,
                    'metadata': local_result.metadata,
                },
                knowledge_search_results=knowledge_results,
                final_analysis=final_analysis,
                timestamp=datetime.now(),
                confidence_score=confidence_score,
            );
            
        except Exception as e:
            print(f'✗ 分析过程中出现错误: {str(e)}');
            raise;
    
    def _generate_search_queries(self, sequence_result, local_result) -> Dict[str, str]:
        """生成知识库搜索查询"""
        queries = {};
        
        # 基于序列本身的查询
        queries['sequence_analysis'] = f'{sequence_result.extracted_sequence} RNA 序列分析';
        
        # 基于序列类型的查询
        if sequence_result.sequence_type:
            queries['sequence_type'] = f'{sequence_result.sequence_type} 序列设计';
        
        # 基于本地函数结果的查询
        if local_result.metadata:
            if 'operation' in local_result.metadata:
                queries['operation'] = f'{local_result.metadata["operation"]} 方法';
            
            if 'gc_content' in local_result.metadata:
                gc_content = local_result.metadata['gc_content'];
                queries['gc_content'] = f'GC 含量 {gc_content:.2f} RNA 序列';
        
        # 基于原始输入文本的查询
        queries['general'] = 'RNA 序列优化 mRNA 设计';
        
        return queries;
    
    def _generate_final_analysis(self, sequence_result, local_result, knowledge_results) -> str:
        """生成最终分析报告（调用大模型生成智能分析）"""
        try:
            # 准备分析数据
            analysis_data = self._prepare_analysis_data(sequence_result, local_result, knowledge_results);
            
            # 调用大模型生成智能分析
            llm_analysis = self._call_llm_for_analysis(analysis_data);
            
            # 组合报告
            analysis_parts = [];
            
            # 序列基本信息
            analysis_parts.append('# RNA 序列分析报告\n');
            analysis_parts.append('## 序列基本信息\n');
            analysis_parts.append(f'- **提取序列**: {sequence_result.extracted_sequence}\n');
            analysis_parts.append(f'- **序列类型**: {sequence_result.sequence_type or "未知"}\n');
            analysis_parts.append(f'- **序列长度**: {len(sequence_result.extracted_sequence)} 个碱基\n');
            analysis_parts.append(f'- **提取置信度**: {sequence_result.confidence:.2f}\n');
            
            # 本地函数处理结果
            analysis_parts.append('\n## 本地函数处理结果\n');
            analysis_parts.append(f'- **执行函数**: {local_result.function_name}\n');
            analysis_parts.append(f'- **执行时间**: {local_result.execution_time:.4f}s\n');
            
            if local_result.metadata:
                analysis_parts.append('- **处理结果详情**:\n');
                for key, value in local_result.metadata.items():
                    analysis_parts.append(f'  - {key}: {value}\n');
            
            # 知识库搜索结果
            analysis_parts.append('\n## 相关知识库搜索结果\n');
            
            total_docs = sum(result['documents_found'] for result in knowledge_results.values());
            analysis_parts.append(f'- **总找到文档数**: {total_docs}\n');
            
            for query_name, result in knowledge_results.items():
                analysis_parts.append(f'\n### {query_name} 搜索结果\n');
                analysis_parts.append(f'- **查询**: {result["query"]}\n');
                analysis_parts.append(f'- **找到文档**: {result["documents_found"]} 篇\n');
                
                if result['documents']:
                    analysis_parts.append('- **相关文档**:\n');
                    for i, doc in enumerate(result['documents'], 1):
                        analysis_parts.append(f'  {i}. {doc["title"]}\n');
                        analysis_parts.append(f'     来源: {doc["source"]}\n');
                        analysis_parts.append(f'     内容预览: {doc["content_preview"]}\n');
            
            # 大模型生成的智能分析
            analysis_parts.append('\n## 智能分析和建议\n');
            analysis_parts.append(f'{llm_analysis}\n');
            
            analysis_parts.append('\n---\n');
            analysis_parts.append(f'*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*\n');
            
            return ''.join(analysis_parts);
            
        except Exception as e:
            print(f'⚠️  大模型分析失败，使用基础分析: {str(e)}');
            return self._generate_basic_analysis(sequence_result, local_result, knowledge_results);
    
    def _prepare_analysis_data(self, sequence_result, local_result, knowledge_results) -> str:
        """准备分析数据供大模型使用"""
        data_parts = [];
        
        data_parts.append('## 分析数据\n');
        data_parts.append(f'**输入序列**: {sequence_result.extracted_sequence}\n');
        data_parts.append(f'**序列类型**: {sequence_result.sequence_type or "未知"}\n');
        data_parts.append(f'**序列长度**: {len(sequence_result.extracted_sequence)} 个碱基\n');
        
        if local_result.metadata:
            data_parts.append('\n**序列分析结果**:\n');
            for key, value in local_result.metadata.items():
                data_parts.append(f'- {key}: {value}\n');
        
        # 添加知识库搜索结果
        total_docs = sum(result['documents_found'] for result in knowledge_results.values());
        data_parts.append(f'\n**相关文献**: 找到 {total_docs} 篇相关文档\n');
        
        for query_name, result in knowledge_results.items():
            if result['documents']:
                data_parts.append(f'\n**{query_name} 相关文献**:\n');
                for i, doc in enumerate(result['documents'][:3], 1):  # 只取前3篇
                    data_parts.append(f'{i}. {doc["title"]}\n');
                    data_parts.append(f'   内容: {doc["content_preview"]}\n');
        
        return ''.join(data_parts);
    
    def _call_llm_for_analysis(self, analysis_data: str) -> str:
        """调用大模型生成智能分析"""
        try:
            # 创建 LLM 实例
            from langchain_openai import ChatOpenAI;
            from langchain.prompts import ChatPromptTemplate;
            
            llm = ChatOpenAI(
                model_name=self.config['model_name'],
                openai_api_base=self.config['openai_api_base'],
                openai_api_key=self.config['openai_api_key'],
                temperature=0.3,  # 较低温度确保分析的一致性
                max_tokens=1500,
                timeout=self.config['timeout'],
                max_retries=self.config['max_retries'],
            );
            
            # 创建提示模板
            prompt_template = ChatPromptTemplate.from_messages([
                ('system', '''你是一个专业的 RNA 序列分析专家。请基于提供的分析数据，生成专业、详细的序列分析报告。

请从以下角度进行分析：
1. **序列特征分析**: 分析序列的基本特征（GC含量、长度、结构等）
2. **功能预测**: 基于序列类型和特征预测可能的功能
3. **优化建议**: 提供具体的序列优化建议
4. **文献关联**: 结合相关文献提供科学依据
5. **风险评估**: 指出潜在的问题和风险
6. **应用建议**: 提供实际应用的建议

请用专业但易懂的语言，提供具体可行的建议。'''),
                ('human', '请分析以下 RNA 序列数据：\n\n{analysis_data}'),
            ]);
            
            # 生成提示
            prompt = prompt_template.format_messages(analysis_data=analysis_data);
            
            # 调用大模型
            response = llm.invoke(prompt);
            print("llm-response: {response}");
            
            return response.content;
            
        except Exception as e:
            print(f'⚠️  大模型调用失败: {str(e)}');
            raise e;
    
    def _generate_basic_analysis(self, sequence_result, local_result, knowledge_results) -> str:
        """生成基础分析报告（备用方案）"""
        analysis_parts = [];
        
        analysis_parts.append('\n## 基础分析和建议\n');
        analysis_parts.append('基于序列分析和知识库搜索，提供以下建议：\n');
        
        # 基于序列特征的建议
        if sequence_result.sequence_type == "5'UTR":
            analysis_parts.append('- 这是一个 5UTR 序列，建议关注翻译效率优化\n');
        
        if local_result.metadata and 'gc_content' in local_result.metadata:
            gc_content = local_result.metadata['gc_content'];
            if gc_content > 0.6:
                analysis_parts.append('- GC 含量较高，可能影响转录效率\n');
            elif gc_content < 0.4:
                analysis_parts.append('- GC 含量较低，可能影响序列稳定性\n');
            else:
                analysis_parts.append('- GC 含量适中，序列设计良好\n');
        
        # 基于知识库的建议
        total_docs = sum(result['documents_found'] for result in knowledge_results.values());
        if total_docs > 0:
            analysis_parts.append('- 找到相关文献资料，建议参考最新的研究成果进行序列优化\n');
        else:
            analysis_parts.append('- 未找到直接相关的文献，建议进行更广泛的文献调研\n');
        
        return ''.join(analysis_parts);
    
    def _calculate_overall_confidence(self, sequence_result, local_result, knowledge_results) -> float:
        """计算整体置信度"""
        confidence = 0.0;
        
        # 序列提取置信度 (40%)
        confidence += sequence_result.confidence * 0.4;
        
        # 本地函数执行成功 (30%)
        if local_result.success:
            confidence += 0.3;
        
        # 知识库搜索结果 (30%)
        total_docs = sum(result['documents_found'] for result in knowledge_results.values());
        if total_docs > 0:
            confidence += min(0.3, total_docs * 0.05);  # 每个文档增加 5% 置信度，最多 30%
        
        return min(confidence, 1.0);
    
    def save_report(self, report: AnalysisReport, filename: Optional[str] = None) -> str:
        """保存报告到文件"""
        if not filename:
            timestamp = report.timestamp.strftime('%Y%m%d_%H%M%S');
            filename = f'rna_analysis_report_{timestamp}.md';
        
        filepath = os.path.join('./reports', filename);
        os.makedirs('./reports', exist_ok=True);
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report.final_analysis);
        
        print(f'✓ 报告已保存到: {filepath}');
        return filepath;


def main():
    """主函数"""
    print('RNA 序列分析系统');
    print('=' * 50);
    
    try:
        # 初始化协调器
        orchestrator = RNAAnalysisOrchestrator();
        
        # 测试用例
        test_input = "我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下";
        
        print(f'\n开始分析测试用例:');
        print(f'输入: {test_input}');
        
        # 执行分析
        report = orchestrator.analyze_sequence(test_input, 'example_function');
        
        # 显示结果摘要
        print(f'\n分析结果摘要:');
        print(f'- 提取序列: {report.extracted_sequence}');
        print(f'- 序列类型: {report.sequence_type}');
        print(f'- 整体置信度: {report.confidence_score:.2f}');
        print(f'- 分析时间: {report.timestamp.strftime("%Y-%m-%d %H:%M:%S")}');
        
        # 保存报告
        report_file = orchestrator.save_report(report);
        
        print(f'\n✓ 分析完成！详细报告请查看: {report_file}');
        
    except Exception as e:
        print(f'\n✗ 系统错误: {str(e)}');
        sys.exit(1);


if __name__ == '__main__':
    main();
