#!/usr/bin/env python3
"""
Sequence Processor
RNA 序列处理工具
"""

import re;
from typing import Dict, List, Any, Optional, Tuple;
from pydantic import BaseModel, Field;


class SequenceAnalysis(BaseModel):
    """序列分析结果"""
    sequence: str = Field(description='RNA 序列');
    length: int = Field(description='序列长度');
    gc_content: float = Field(description='GC 含量');
    at_content: float = Field(description='AT 含量');
    has_start_codon: bool = Field(description='是否包含起始密码子');
    has_stop_codon: bool = Field(description='是否包含终止密码子');
    potential_orf: Optional[str] = Field(description='潜在开放阅读框');
    secondary_structure: Optional[str] = Field(description='二级结构预测');
    quality_score: float = Field(description='序列质量评分 (0-1)');


class SequenceProcessor:
    """序列处理器"""
    
    def __init__(self):
        self.start_codons = ['AUG', 'GUG', 'UUG'];
        self.stop_codons = ['UAA', 'UAG', 'UGA'];
        self.valid_bases = set('AUGC');
    
    def process_sequence(self, sequence: str) -> SequenceAnalysis:
        """处理 RNA 序列"""
        # 清理和验证序列
        clean_sequence = self._clean_sequence(sequence);
        
        if not clean_sequence:
            raise ValueError('无效的 RNA 序列');
        
        # 分析序列
        length = len(clean_sequence);
        gc_content = self._calculate_gc_content(clean_sequence);
        at_content = 1.0 - gc_content;
        has_start_codon = self._has_start_codon(clean_sequence);
        has_stop_codon = self._has_stop_codon(clean_sequence);
        potential_orf = self._find_potential_orf(clean_sequence);
        secondary_structure = self._predict_secondary_structure(clean_sequence);
        quality_score = self._calculate_quality_score(clean_sequence);
        
        return SequenceAnalysis(
            sequence=clean_sequence,
            length=length,
            gc_content=gc_content,
            at_content=at_content,
            has_start_codon=has_start_codon,
            has_stop_codon=has_stop_codon,
            potential_orf=potential_orf,
            secondary_structure=secondary_structure,
            quality_score=quality_score,
        );
    
    def _clean_sequence(self, sequence: str) -> str:
        """清理序列"""
        # 转换为大写
        sequence = sequence.upper();
        
        # 移除空格和特殊字符
        sequence = re.sub(r'[^AUGC]', '', sequence);
        
        # 验证是否只包含有效碱基
        if not all(base in self.valid_bases for base in sequence):
            return '';
        
        return sequence;
    
    def _calculate_gc_content(self, sequence: str) -> float:
        """计算 GC 含量"""
        if not sequence:
            return 0.0;
        
        gc_count = sequence.count('G') + sequence.count('C');
        return gc_count / len(sequence);
    
    def _has_start_codon(self, sequence: str) -> bool:
        """检查是否包含起始密码子"""
        for codon in self.start_codons:
            if codon in sequence:
                return True;
        return False;
    
    def _has_stop_codon(self, sequence: str) -> bool:
        """检查是否包含终止密码子"""
        for codon in self.stop_codons:
            if codon in sequence:
                return True;
        return False;
    
    def _find_potential_orf(self, sequence: str) -> Optional[str]:
        """查找潜在的开放阅读框"""
        # 简化的 ORF 查找
        for i in range(len(sequence) - 2):
            codon = sequence[i:i+3];
            if codon in self.start_codons:
                # 查找对应的终止密码子
                for j in range(i + 3, len(sequence) - 2, 3):
                    stop_codon = sequence[j:j+3];
                    if stop_codon in self.stop_codons:
                        return sequence[i:j+3];
        return None;
    
    def _predict_secondary_structure(self, sequence: str) -> str:
        """预测二级结构（简化版本）"""
        # 这是一个简化的二级结构预测
        # 实际应用中应该使用专门的工具如 RNAfold
        
        structure = '';
        for i, base in enumerate(sequence):
            if i < len(sequence) - 1:
                next_base = sequence[i + 1];
                # 简单的配对规则
                if (base == 'A' and next_base == 'U') or (base == 'U' and next_base == 'A'):
                    structure += '(';
                elif (base == 'G' and next_base == 'C') or (base == 'C' and next_base == 'G'):
                    structure += ')';
                else:
                    structure += '.';
            else:
                structure += '.';
        
        return structure;
    
    def _calculate_quality_score(self, sequence: str) -> float:
        """计算序列质量评分"""
        if not sequence:
            return 0.0;
        
        score = 0.5;  # 基础分数
        
        # 长度评分
        length = len(sequence);
        if 10 <= length <= 100:
            score += 0.2;
        elif 100 < length <= 1000:
            score += 0.3;
        elif length > 1000:
            score += 0.1;
        
        # GC 含量评分
        gc_content = self._calculate_gc_content(sequence);
        if 0.4 <= gc_content <= 0.6:
            score += 0.2;
        elif 0.3 <= gc_content <= 0.7:
            score += 0.1;
        
        # 起始密码子评分
        if self._has_start_codon(sequence):
            score += 0.1;
        
        return min(score, 1.0);
    
    def compare_sequences(self, seq1: str, seq2: str) -> Dict[str, Any]:
        """比较两个序列"""
        analysis1 = self.process_sequence(seq1);
        analysis2 = self.process_sequence(seq2);
        
        # 计算相似性
        similarity = self._calculate_similarity(seq1, seq2);
        
        return {
            'sequence1_analysis': analysis1,
            'sequence2_analysis': analysis2,
            'similarity': similarity,
            'length_difference': abs(analysis1.length - analysis2.length),
            'gc_content_difference': abs(analysis1.gc_content - analysis2.gc_content),
        };
    
    def _calculate_similarity(self, seq1: str, seq2: str) -> float:
        """计算序列相似性（简化版本）"""
        if not seq1 or not seq2:
            return 0.0;
        
        # 使用简单的字符匹配
        matches = sum(1 for a, b in zip(seq1, seq2) if a == b);
        max_length = max(len(seq1), len(seq2));
        
        return matches / max_length if max_length > 0 else 0.0;
