#!/usr/bin/env python3
"""
流式 RNA 序列分析协调器
支持实时输出分析过程的每个步骤
"""

import sys
import os
from typing import Dict, List, Any, Optional, Generator, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.sequence_parser import SequenceParserAgent
from agents.rag_agent import RAGAgent
from utils.local_functions import LocalSequenceFunction
from config.api_config import validate_config


class StreamingStep(BaseModel):
    """流式步骤"""
    step_number: int = Field(description='步骤编号')
    step_name: str = Field(description='步骤名称')
    status: str = Field(description='状态: running, completed, failed')
    message: str = Field(description='状态消息')
    data: Optional[Dict[str, Any]] = Field(description='步骤数据')
    timestamp: datetime = Field(description='时间戳')


class StreamingAnalysisReport(BaseModel):
    """流式分析报告"""
    input_text: str = Field(description='输入的自然语言文本')
    extracted_sequence: Optional[str] = Field(description='提取的 RNA 序列')
    sequence_type: Optional[str] = Field(description='序列类型')
    confidence_score: float = Field(description='提取置信度')
    local_function_result: Optional[Dict[str, Any]] = Field(description='本地函数处理结果')
    knowledge_search_results: Optional[Dict[str, Any]] = Field(description='知识库搜索结果')
    final_analysis: Optional[str] = Field(description='最终分析报告')
    steps: List[StreamingStep] = Field(description='分析步骤')
    timestamp: datetime = Field(description='分析时间')
    overall_confidence: float = Field(description='整体置信度')


class StreamingRNAAnalysisOrchestrator:
    """流式 RNA 分析协调器"""
    
    def __init__(self, knowledge_base_path: str = './data'):
        """初始化协调器"""
        # 验证配置
        if not validate_config():
            raise ValueError('API 配置不完整，请检查 .env 文件')

        # 获取配置
        from config.api_config import get_langchain_config
        self.config = get_langchain_config()

        # 初始化各个组件
        self.sequence_parser = SequenceParserAgent()
        self.rag_agent = RAGAgent(knowledge_base_path)
        self.local_functions = LocalSequenceFunction()
    
    def stream_analyze_sequence(
        self,
        user_input: str,
        function_name: str = 'example_function'
    ) -> Generator[Tuple[StreamingStep, Optional[StreamingAnalysisReport]], None, None]:
        """流式分析序列"""
        
        # 初始化报告
        report = StreamingAnalysisReport(
            input_text=user_input,
            extracted_sequence=None,
            sequence_type=None,
            confidence_score=0.0,
            local_function_result=None,
            knowledge_search_results=None,
            final_analysis=None,
            steps=[],
            timestamp=datetime.now(),
            overall_confidence=0.0
        )
        
        try:
            # 步骤 1: 序列解析
            step1 = StreamingStep(
                step_number=1,
                step_name="序列解析",
                status="running",
                message="正在解析自然语言并提取 RNA 序列...",
                data=None,
                timestamp=datetime.now()
            )
            report.steps.append(step1)
            yield step1, report
            
            # 执行序列解析
            sequence_result = self.sequence_parser.parse_sequence(user_input)
            
            if not sequence_result.extracted_sequence:
                step1.status = "failed"
                step1.message = f"序列提取失败: {sequence_result.error_message}"
                yield step1, report
                return
            
            # 更新报告
            report.extracted_sequence = sequence_result.extracted_sequence
            report.sequence_type = sequence_result.sequence_type
            report.confidence_score = sequence_result.confidence
            
            step1.status = "completed"
            step1.message = f"成功提取序列: {sequence_result.extracted_sequence}"
            step1.data = {
                'extracted_sequence': sequence_result.extracted_sequence,
                'sequence_type': sequence_result.sequence_type,
                'confidence': sequence_result.confidence,
                'length': len(sequence_result.extracted_sequence)
            }
            yield step1, report
            
            # 步骤 2: 本地函数处理
            step2 = StreamingStep(
                step_number=2,
                step_name="本地函数处理",
                status="running",
                message=f"正在执行本地函数: {function_name}...",
                data=None,
                timestamp=datetime.now()
            )
            report.steps.append(step2)
            yield step2, report
            
            # 执行本地函数
            local_result = self.local_functions.execute_function(
                function_name, 
                sequence_result.extracted_sequence
            )
            
            if not local_result.success:
                step2.status = "failed"
                step2.message = f"本地函数执行失败: {local_result.error_message}"
                yield step2, report
                return
            
            # 更新报告
            report.local_function_result = {
                'function_name': local_result.function_name,
                'success': local_result.success,
                'output_sequence': local_result.output_sequence,
                'metadata': local_result.metadata,
                'execution_time': local_result.execution_time
            }
            
            step2.status = "completed"
            step2.message = f"本地函数 '{function_name}' 执行成功"
            step2.data = report.local_function_result
            yield step2, report
            
            # 步骤 3: 知识库搜索
            step3 = StreamingStep(
                step_number=3,
                step_name="知识库搜索",
                status="running",
                message="正在生成搜索查询...",
                data=None,
                timestamp=datetime.now()
            )
            report.steps.append(step3)
            yield step3, report
            
            # 生成搜索查询
            search_queries = self._generate_search_queries(sequence_result, local_result)
            
            step3.message = f"正在搜索知识库 ({len(search_queries)} 个查询)..."
            yield step3, report
            
            # 执行知识库搜索
            knowledge_results = {}
            total_docs = 0
            
            for i, (query_name, query) in enumerate(search_queries.items(), 1):
                step3.message = f"正在搜索 ({i}/{len(search_queries)}): {query[:50]}..."
                yield step3, report
                
                result = self.rag_agent.search_knowledge(query, top_k=3)
                knowledge_results[query_name] = {
                    'query': query,
                    'documents_found': len(result.documents),
                    'summary': result.summary,
                    'documents': [
                        {
                            'title': doc.metadata.get('title', '无标题'),
                            'content_preview': doc.page_content[:200] + '...' if len(doc.page_content) > 200 else doc.page_content,
                            'source': doc.metadata.get('source', '未知'),
                        }
                        for doc in result.documents
                    ]
                }
                total_docs += len(result.documents)
            
            # 更新报告
            report.knowledge_search_results = knowledge_results
            
            step3.status = "completed"
            step3.message = f"知识库搜索完成，找到 {total_docs} 篇相关文档"
            step3.data = {'total_documents': total_docs, 'queries': len(search_queries)}
            yield step3, report
            
            # 步骤 4: 生成最终分析
            step4 = StreamingStep(
                step_number=4,
                step_name="智能分析",
                status="running",
                message="正在生成智能分析报告...",
                data=None,
                timestamp=datetime.now()
            )
            report.steps.append(step4)
            yield step4, report
            
            try:
                final_analysis = self._generate_final_analysis(
                    sequence_result, local_result, knowledge_results
                )
                report.final_analysis = final_analysis
                
                step4.status = "completed"
                step4.message = "智能分析报告生成完成"
                
            except Exception as e:
                # 使用基础分析作为备用
                basic_analysis = self._generate_basic_analysis(
                    sequence_result, local_result, knowledge_results
                )
                report.final_analysis = f"⚠️ 智能分析生成失败，使用基础分析:\n\n{basic_analysis}"
                
                step4.status = "completed"
                step4.message = f"使用基础分析 (智能分析失败: {str(e)})"
            
            # 计算整体置信度
            report.overall_confidence = self._calculate_overall_confidence(
                sequence_result, local_result, knowledge_results
            )
            
            step4.data = {'analysis_length': len(report.final_analysis)}
            yield step4, report
            
        except Exception as e:
            # 创建错误步骤
            error_step = StreamingStep(
                step_number=len(report.steps) + 1,
                step_name="错误处理",
                status="failed",
                message=f"分析过程中出现错误: {str(e)}",
                data={'error': str(e)},
                timestamp=datetime.now()
            )
            report.steps.append(error_step)
            yield error_step, report
    
    def _generate_search_queries(self, sequence_result, local_result) -> Dict[str, str]:
        """生成搜索查询"""
        queries = {}
        
        # 基于序列类型的查询
        if sequence_result.sequence_type:
            queries['sequence_type'] = f"{sequence_result.sequence_type} RNA sequence analysis"
        
        # 基于序列内容的查询
        if sequence_result.extracted_sequence:
            queries['sequence_content'] = f"RNA sequence {sequence_result.extracted_sequence[:20]} analysis"
        
        # 基于本地函数结果的查询
        if local_result.success and local_result.metadata:
            if 'gc_content' in local_result.metadata:
                gc = local_result.metadata['gc_content']
                queries['gc_content'] = f"RNA GC content {gc:.2f} optimization"
        
        # 通用查询
        queries['general'] = "mRNA design optimization NGF"
        
        return queries
    
    def _generate_final_analysis(self, sequence_result, local_result, knowledge_results) -> str:
        """使用大模型生成最终智能分析"""
        try:
            from langchain_openai import ChatOpenAI
            from langchain.schema import HumanMessage

            # 初始化大模型
            llm = ChatOpenAI(
                model_name=self.config['model_name'],
                openai_api_base=self.config['openai_api_base'],
                openai_api_key=self.config['openai_api_key'],
                temperature=0.3,
                max_tokens=2000,
                timeout=60,
            )

            # 构建知识库摘要
            knowledge_summary = ""
            total_docs = 0
            for query_name, result in knowledge_results.items():
                total_docs += result['documents_found']
                if result['documents']:
                    knowledge_summary += f"\n**{query_name.replace('_', ' ').title()}**:\n"
                    for doc in result['documents'][:2]:  # 只取前2个文档
                        knowledge_summary += f"- {doc['content_preview']}\n"

            # 构建本地分析摘要
            local_summary = f"执行了 {local_result.function_name} 函数，"
            if local_result.success:
                local_summary += "执行成功。"
                if local_result.metadata:
                    local_summary += f" 主要结果: {local_result.metadata}"
            else:
                local_summary += f"执行失败: {local_result.error_message}"

            # 构建提示词
            prompt = f"""请作为一名专业的生物信息学专家，对以下RNA序列进行全面的智能分析：

## 基本信息
- **输入文本**: {sequence_result.input_text}
- **提取序列**: {sequence_result.extracted_sequence}
- **序列类型**: {sequence_result.sequence_type or '未知'}
- **序列长度**: {len(sequence_result.extracted_sequence)} 个碱基
- **提取置信度**: {sequence_result.confidence:.2f}

## 本地分析结果
{local_summary}

## 相关文献知识 (共找到 {total_docs} 篇文档)
{knowledge_summary}

请基于以上信息，提供一个专业、详细的RNA序列分析报告，包括：

1. **序列特征分析**: 分析序列的组成、结构特点
2. **功能预测**: 基于序列类型和特征预测可能的生物学功能
3. **设计评价**: 评价当前序列设计的优缺点
4. **优化建议**: 提供具体的序列优化建议
5. **实验建议**: 推荐相关的实验验证方法
6. **文献支持**: 结合检索到的文献信息提供科学依据

请用中文回答，保持专业性和准确性。"""

            # 调用大模型
            message = HumanMessage(content=prompt)
            response = llm.invoke([message])

            return response.content

        except Exception as e:
            # 如果大模型调用失败，返回基础分析
            return self._generate_basic_analysis(sequence_result, local_result, knowledge_results)
    
    def _generate_basic_analysis(self, sequence_result, local_result, knowledge_results) -> str:
        """生成基础分析"""
        return self._generate_final_analysis(sequence_result, local_result, knowledge_results)
    
    def _calculate_overall_confidence(self, sequence_result, local_result, knowledge_results) -> float:
        """计算整体置信度"""
        confidence_factors = []
        
        # 序列提取置信度
        confidence_factors.append(sequence_result.confidence)
        
        # 本地函数执行成功率
        confidence_factors.append(1.0 if local_result.success else 0.0)
        
        # 知识库搜索成功率
        total_docs = sum(r['documents_found'] for r in knowledge_results.values())
        knowledge_confidence = min(1.0, total_docs / 10.0)  # 假设10篇文档为满分
        confidence_factors.append(knowledge_confidence)
        
        return sum(confidence_factors) / len(confidence_factors)


if __name__ == "__main__":
    # 测试流式分析
    orchestrator = StreamingRNAAnalysisOrchestrator()
    
    test_input = "我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下"
    
    print("🚀 开始流式分析...")
    for step, report in orchestrator.stream_analyze_sequence(test_input, 'example_function'):
        print(f"[{step.step_number}/4] {step.step_name}: {step.status} - {step.message}")
        if step.status == "completed" and step.data:
            print(f"    数据: {step.data}")
        print()
    
    print("✅ 流式分析完成!")
