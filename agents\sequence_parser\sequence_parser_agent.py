#!/usr/bin/env python3
"""
Sequence Parser Agent
从自然语言中提取 RNA 序列的 LangChain Agent
"""

import re;
from typing import Dict, List, Any, Optional;
from pydantic import BaseModel, Field;
from langchain.agents import AgentExecutor, create_openai_tools_agent;
from langchain.tools import BaseTool;
from langchain.schema import BaseMessage;
from langchain_openai import ChatOpenAI;
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder;
from langchain.memory import ConversationBufferMemory;
from langchain.schema import HumanMessage, AIMessage;

# config import moved to avoid circular import issues


class SequenceExtractionResult(BaseModel):
    """序列提取结果"""
    input_text: str = Field(description='输入的自然语言文本');
    extracted_sequence: Optional[str] = Field(description='提取的 RNA 序列');
    sequence_type: Optional[str] = Field(description='序列类型 (5UTR, 3UTR, CDS, etc.)');
    confidence: float = Field(description='提取置信度 (0-1)');
    error_message: Optional[str] = Field(description='错误信息');


class SequenceExtractionTool(BaseTool):
    """序列提取工具 - 使用大模型进行智能提取"""

    name: str = 'extract_sequence';
    description: str = '从自然语言文本中智能提取 RNA 序列，使用大模型进行分析';

    def __init__(self):
        super().__init__();
        # 使用类变量存储配置，避免Pydantic字段冲突
        from config.api_config import get_langchain_config;
        # 使用 object.__setattr__ 来设置属性，避免 Pydantic 验证
        object.__setattr__(self, '_config', get_langchain_config());
        object.__setattr__(self, '_llm', self._initialize_llm());

    def _initialize_llm(self):
        # print(self._config, "self._config")
        """初始化大模型"""
        return ChatOpenAI(
            model_name=self._config['model_name'],
            openai_api_base=self._config['openai_api_base'],
            openai_api_key=self._config['openai_api_key'],
            temperature=0.1,  # 低温度确保准确性
            max_tokens=500,
            timeout=30,
        );

    def _run(self, text: str) -> str:
        """执行序列提取"""
        return self._extract_sequence_with_llm(text);

    def _extract_sequence_with_llm(self, text: str) -> str:
        """使用大模型从文本中提取 RNA 序列"""
        try:
            # 构建提示词
            prompt = f"""请从以下文本中提取RNA序列信息：

文本："{text}"

请按照以下要求分析：
1. 识别文本中的RNA或DNA序列（由A、T/U、G、C组成的字符串）
2. 如果是DNA序列（含T），请转换为RNA序列（T→U）
3. 提取序列的类型信息（如5'UTR、3'UTR、CDS、mRNA等）
4. 验证序列的有效性

请以以下格式回答：
序列：[提取的序列]
类型：[序列类型]
长度：[序列长度]
有效性：[是否有效]

如果没有找到序列，请回答"未找到有效的RNA序列"。
只返回分析结果，不要添加其他解释。"""

            # 调用大模型
            message = HumanMessage(content=prompt);
            response = self._llm.invoke([message]);

            # 解析大模型响应
            return self._parse_llm_response(response.content, text);

        except Exception as e:
            # 如果大模型调用失败，回退到正则表达式方法
            print(f"大模型提取失败，使用正则表达式回退: {e}");
            return self._fallback_regex_extraction(text);

    def _parse_llm_response(self, response: str, original_text: str) -> str:
        """解析大模型的响应"""
        if "未找到有效的RNA序列" in response:
            return "未找到有效的RNA序列";

        # 尝试从响应中提取序列信息
        lines = response.strip().split('\n');
        sequence = None;
        seq_type = None;
        length = None;

        for line in lines:
            line = line.strip();
            if line.startswith('序列：') or line.startswith('序列:'):
                sequence = line.split('：', 1)[-1].split(':', 1)[-1].strip();
            elif line.startswith('类型：') or line.startswith('类型:'):
                seq_type = line.split('：', 1)[-1].split(':', 1)[-1].strip();
            elif line.startswith('长度：') or line.startswith('长度:'):
                length = line.split('：', 1)[-1].split(':', 1)[-1].strip();

        # 验证提取的序列
        if sequence and self._validate_sequence(sequence):
            result = f"找到 RNA 序列: {sequence}";
            if length:
                result += f" (长度: {length})";
            elif sequence:
                result += f" (长度: {len(sequence)})";
            if seq_type and seq_type != "未知":
                result += f" (类型: {seq_type})";
            return result;
        else:
            # 如果大模型提取失败，尝试正则表达式回退
            return self._fallback_regex_extraction(original_text);

    def _validate_sequence(self, sequence: str) -> bool:
        """验证序列是否有效"""
        if not sequence:
            return False;

        # 清理序列（移除空格、标点等）
        clean_seq = re.sub(r'[^AUGC]', '', sequence.upper());

        # 检查是否至少有5个有效碱基
        return len(clean_seq) >= 5 and re.match(r'^[AUGC]+$', clean_seq);

    def _fallback_regex_extraction(self, text: str) -> str:
        """正则表达式回退方法"""
        # RNA 序列模式：包含 A, U, G, C 的连续字符串，最少5个碱基
        rna_pattern = r'[AUGC]{5,}';
        sequences = re.findall(rna_pattern, text.upper());

        # 也尝试 DNA 序列模式（T 替换 U）
        dna_pattern = r'[ATGC]{5,}';
        dna_sequences = re.findall(dna_pattern, text.upper());

        # 将 DNA 序列转换为 RNA 序列
        rna_from_dna = [seq.replace('T', 'U') for seq in dna_sequences];

        # 合并所有找到的序列
        all_sequences = sequences + rna_from_dna;

        if all_sequences:
            # 返回最长的序列
            longest_sequence = max(all_sequences, key=len);
            return f'找到 RNA 序列: {longest_sequence} (长度: {len(longest_sequence)}) [正则表达式提取]';
        else:
            return '未找到有效的 RNA 序列';


class SequenceParserAgent:
    """序列解析 Agent"""
    
    def __init__(self):
        from config.api_config import get_langchain_config;
        self.config = get_langchain_config();
        self.llm = self._initialize_llm();
        self.tools = [SequenceExtractionTool()];
        self.agent_executor = self._create_agent();
    
    def _initialize_llm(self):
        """初始化 LLM"""
        return ChatOpenAI(
            model_name=self.config['model_name'],
            openai_api_base=self.config['openai_api_base'],
            openai_api_key=self.config['openai_api_key'],
            temperature=self.config['temperature'],
            max_tokens=self.config['max_tokens'],
            timeout=self.config['timeout'],
            max_retries=self.config['max_retries'],
        );
    
    def _create_agent(self):
        """创建 Agent"""
        # 定义提示模板
        prompt = ChatPromptTemplate.from_messages([
            ('system', '''你是一个专业的 RNA 序列分析专家。你的任务是：

1. 从用户输入的自然语言中识别和提取 RNA 序列
2. 识别序列的类型（如 5'UTR、3'UTR、CDS 等）
3. 验证序列的有效性（只包含 A, U, G, C）
4. 提供序列的详细分析和评价

请使用 extract_sequence 工具来提取序列，然后基于你的专业知识分析结果。

示例：
用户输入："我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA"
你应该：
1. 使用工具提取序列 "ACCGGGCCCTTTGTA"
2. 识别这是 5'UTR 序列
3. 分析序列特征（GC含量、长度、潜在功能等）
4. 提供专业的评价和建议

请始终以专业、详细的方式提供分析结果。'''),
            ('human', '{input}'),
            MessagesPlaceholder(variable_name='agent_scratchpad'),
        ]);
        
        # 创建 Agent
        agent = create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt,
        );
        
        # 创建 Agent 执行器
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3,
        );
        
        return agent_executor;
    
    def parse_sequence(self, input_text: str) -> SequenceExtractionResult:
        """解析序列"""
        try:
            # 先尝试直接使用大模型提取工具
            extraction_tool = SequenceExtractionTool();
            tool_result = extraction_tool._run(input_text);

            # 从工具结果中提取序列
            extracted_sequence = self._extract_sequence_from_tool_result(tool_result);
            sequence_type = self._extract_sequence_type_from_response(tool_result, input_text);
            confidence = self._calculate_confidence(tool_result, extracted_sequence);

            # 如果直接提取失败，再使用Agent
            if not extracted_sequence:
                # 转义输入文本中的特殊字符
                safe_input = input_text.replace("'", "''").replace('"', '""');

                result = self.agent_executor.invoke({
                    'input': f'请分析以下文本并提取 RNA 序列：{safe_input}',
                });

                # 从Agent结果中提取信息
                response = result.get('output', '');
                extracted_sequence = self._extract_sequence_from_response(response);
                sequence_type = self._extract_sequence_type_from_response(response, input_text);
                confidence = self._calculate_confidence(response, extracted_sequence);

            return SequenceExtractionResult(
                input_text=input_text,
                extracted_sequence=extracted_sequence,
                sequence_type=sequence_type,
                confidence=confidence,
                error_message=None,
            );

        except Exception as e:
            return SequenceExtractionResult(
                input_text=input_text,
                extracted_sequence=None,
                sequence_type=None,
                confidence=0.0,
                error_message=str(e),
            );
    
    def _extract_sequence_from_tool_result(self, tool_result: str) -> Optional[str]:
        """从工具结果中提取序列"""
        if "未找到有效的RNA序列" in tool_result:
            return None;

        # 查找 "找到 RNA 序列: " 后面的序列
        import re;
        pattern = r'找到 RNA 序列:\s*([AUGC]+)';
        match = re.search(pattern, tool_result);
        if match:
            return match.group(1);

        # 回退到正则表达式提取
        return self._extract_sequence_from_response(tool_result);

    def _extract_sequence_from_response(self, response: str) -> Optional[str]:
        """从 Agent 响应中提取序列"""
        # 查找 RNA 序列模式，最少5个碱基
        rna_pattern = r'[AUGC]{5,}';
        sequences = re.findall(rna_pattern, response.upper());

        # 也查找 DNA 序列模式
        dna_pattern = r'[ATGC]{5,}';
        dna_sequences = re.findall(dna_pattern, response.upper());

        # 将 DNA 序列转换为 RNA 序列
        rna_from_dna = [seq.replace('T', 'U') for seq in dna_sequences];

        # 合并所有序列
        all_sequences = sequences + rna_from_dna;

        if all_sequences:
            return max(all_sequences, key=len);
        return None;
    
    def _extract_sequence_type_from_response(self, response: str, input_text: str) -> Optional[str]:
        """从响应中提取序列类型"""
        text = (response + ' ' + input_text).lower();
        
        if "5'utr" in text or "5utr" in text or "five prime utr" in text:
            return "5'UTR";
        elif "3'utr" in text or "3utr" in text or "three prime utr" in text:
            return "3'UTR";
        elif "cds" in text or "coding sequence" in text:
            return "CDS";
        elif "mrna" in text:
            return "mRNA";
        else:
            return "Unknown";
    
    def _calculate_confidence(self, response: str, sequence: Optional[str]) -> float:
        """计算提取置信度"""
        if not sequence:
            return 0.0;
        
        confidence = 0.5;  # 基础置信度
        
        # 如果响应中包含明确的序列信息，增加置信度
        if '序列' in response or 'sequence' in response.lower():
            confidence += 0.2;
        
        # 如果序列长度合理，增加置信度
        if 10 <= len(sequence) <= 1000:
            confidence += 0.2;
        
        # 如果响应中包含序列类型信息，增加置信度
        if any(keyword in response.lower() for keyword in ['utr', 'cds', 'mrna']):
            confidence += 0.1;
        
        return min(confidence, 1.0);


# 使用示例
if __name__ == '__main__':
    import sys;
    import os;
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))));

    agent = SequenceParserAgent();

    test_input = "我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下";
    result = agent.parse_sequence(test_input);

    print('序列解析结果:');
    print(f'输入文本: {result.input_text}');
    print(f'提取序列: {result.extracted_sequence}');
    print(f'序列类型: {result.sequence_type}');
    print(f'置信度: {result.confidence}');
    if result.error_message:
        print(f'错误信息: {result.error_message}');
