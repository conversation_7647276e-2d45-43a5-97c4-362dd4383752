#!/usr/bin/env python3
"""
RNA 序列分析系统 - Gradio Web 界面
基于 LangChain 的智能生物信息学分析平台
"""

import sys
import os
import gradio as gr
import traceback
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 全局变量
orchestrator = None
system_initialized = False
init_error = None


def initialize_system():
    """初始化系统"""
    global orchestrator, system_initialized, init_error
    
    if system_initialized:
        return True
    
    try:
        from main import RNAAnalysisOrchestrator
        from config.api_config import validate_config
        
        if not validate_config():
            init_error = "❌ API 配置不完整，请检查 .env 文件"
            return False
        
        orchestrator = RNAAnalysisOrchestrator()
        system_initialized = True
        return True
        
    except Exception as e:
        init_error = f"❌ 系统初始化失败: {str(e)}"
        return False


def analyze_rna_sequence(user_input: str, function_name: str):
    """分析 RNA 序列"""
    
    # 检查输入
    if not user_input or not user_input.strip():
        return "⚠️ 请输入包含 RNA 序列的描述", "", "", ""
    
    # 初始化系统
    if not initialize_system():
        return init_error, "", "", ""
    
    try:
        # 执行分析
        report = orchestrator.analyze_sequence(user_input, function_name)
        
        # 格式化基本信息
        basic_info = f"""
## 📊 序列基本信息

- **提取序列**: `{report.extracted_sequence or '未提取到'}`
- **序列类型**: {report.sequence_type or '未知'}
- **序列长度**: {len(report.extracted_sequence) if report.extracted_sequence else 0} 个碱基
- **提取置信度**: {report.confidence_score:.2f}
- **分析时间**: {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

## 🔬 本地函数处理结果

- **执行函数**: {report.local_function_result['function_name']}
- **执行状态**: {'✅ 成功' if report.local_function_result['success'] else '❌ 失败'}
- **执行时间**: {report.local_function_result['execution_time']:.4f} 秒

**详细结果**:
"""
        
        # 添加本地函数结果详情
        if report.local_function_result['metadata']:
            for key, value in report.local_function_result['metadata'].items():
                if isinstance(value, float):
                    basic_info += f"- **{key}**: {value:.3f}\n"
                else:
                    basic_info += f"- **{key}**: {value}\n"
        
        # 格式化知识库搜索结果
        knowledge_results = report.knowledge_search_results
        total_docs = sum(result['documents_found'] for result in knowledge_results.values())
        
        knowledge_info = f"""
## 📚 知识库搜索结果

**总找到文档数**: {total_docs} 篇

"""
        
        for query_name, result in knowledge_results.items():
            knowledge_info += f"### {query_name.replace('_', ' ').title()}\n"
            knowledge_info += f"- **查询**: {result['query']}\n"
            knowledge_info += f"- **找到文档**: {result['documents_found']} 篇\n"
            
            if result['documents']:
                knowledge_info += "- **相关文档**:\n"
                for i, doc in enumerate(result['documents'][:3], 1):  # 只显示前3个
                    knowledge_info += f"  {i}. **{doc['title']}**\n"
                    knowledge_info += f"     - 来源: {doc['source']}\n"
                    knowledge_info += f"     - 内容: {doc['content_preview'][:100]}...\n"
            knowledge_info += "\n"
        
        # 完整报告
        full_report = report.final_analysis if report.final_analysis else "报告生成中..."
        
        # 状态信息
        status = f"✅ 分析完成!\n🧬 序列: {report.extracted_sequence}\n🏷️ 类型: {report.sequence_type}\n🎯 置信度: {report.confidence_score:.2f}"
        
        return status, basic_info, knowledge_info, full_report
        
    except Exception as e:
        error_msg = f"❌ 分析失败: {str(e)}"
        error_detail = f"错误详情:\n{traceback.format_exc()}"
        return error_msg, error_detail, "", ""


# 预定义示例
EXAMPLES = [
    ["优化 ngf 的 5utr 序列 GCTCCGGCACAGCAGAGAGCGCTGGGAGCCGGAGGGGAGCGCAGCGAGTTTTGGCCAGTGGTCGTGCAGTCCAAGGGGCTGGATGGCATGCTGGACCCAAGCTCAGCTCAGCGTCCGGACCCAATAACAGTTTTACCAAGGGAGCAGCTTTCTATCCTGGCCACACTGAGGTGCATAGCGTA", "example_function"],

]

# 函数选项
FUNCTION_OPTIONS = [
    ("示例函数", "example_function"),
]


def create_interface():
    """创建 Gradio 界面"""
    
    with gr.Blocks(
        title="RNA 序列分析系统",
        theme=gr.themes.Soft(),
        css="""
        .main-header {
            text-align: center;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        """
    ) as demo:
        
        # 标题
        gr.HTML("""
        <div class="main-header">
            <h1>🧬 RNA 序列分析系统</h1>
            <h3>基于 LangChain 的智能生物信息学分析平台</h3>
            <p>🤖 双Agent架构 | 📚 29个PDF知识库 | 🔬 多种分析功能 | 🧠 大模型智能分析</p>
        </div>
        """)
        
        with gr.Row():
            # 左侧输入区域
            with gr.Column(scale=1):
                gr.Markdown("## 📝 输入区域")
                
                user_input = gr.Textbox(
                    label="RNA 序列描述",
                    placeholder="请输入包含 RNA 序列的自然语言描述，例如：\n我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下",
                    lines=5,
                    max_lines=10
                )
                
                function_choice = gr.Dropdown(
                    choices=FUNCTION_OPTIONS,
                    value="example_function",
                    label="选择分析函数",
                    info="选择要执行的本地序列处理函数"
                )
                
                with gr.Row():
                    analyze_btn = gr.Button("🚀 开始分析", variant="primary", size="lg")
                    clear_btn = gr.Button("🗑️ 清空", variant="secondary")
                
                # 示例
                gr.Markdown("## 💡 使用示例")
                gr.Examples(
                    examples=EXAMPLES,
                    inputs=[user_input, function_choice],
                    label="点击示例快速开始"
                )
            
            # 右侧结果区域
            with gr.Column(scale=2):
                gr.Markdown("## 📊 分析结果")
                
                # 状态显示
                status_output = gr.Textbox(
                    label="分析状态",
                    lines=4,
                    interactive=False
                )
                
                # 结果标签页
                with gr.Tabs():
                    with gr.TabItem("📊 基本信息"):
                        basic_info_output = gr.Markdown()
                    
                    with gr.TabItem("📚 知识库"):
                        knowledge_results_output = gr.Markdown()
                    
                    with gr.TabItem("📋 完整报告"):
                        full_report_output = gr.Markdown()
        
        # 系统信息
        with gr.Accordion("ℹ️ 系统信息", open=False):
            gr.Markdown("""
            ### 🎯 系统特点
            - **Agent 1**: 智能序列解析 (LangChain OpenAI Tools Agent)
            - **Agent 2**: RAG 知识库检索 (ChromaDB + 29个PDF文档)
            - **本地函数**: 多种序列分析功能
            - **大模型**: gemini-2.5-flash 智能分析
            - **知识库**: 3427个文档块，涵盖 mRNA 设计、NGF 研究等
            
            ### 🔧 可用分析函数
            - **计算序列指标**: GC含量、长度、质量分数等
            - **反向互补序列**: 生成反向互补序列
            - **翻译为蛋白质**: RNA → 蛋白质序列翻译
            - **优化密码子**: 优化密码子使用偏好
            - **预测二级结构**: RNA 二级结构预测
            
            ### 🌐 访问信息
            - **本地地址**: http://127.0.0.1:7865
            - **状态**: 运行中
            """)
        
        # 事件绑定
        analyze_btn.click(
            fn=analyze_rna_sequence,
            inputs=[user_input, function_choice],
            outputs=[
                status_output,
                basic_info_output,
                knowledge_results_output,
                full_report_output
            ]
        )
        
        clear_btn.click(
            fn=lambda: ("", "example_function", "", "", "", ""),
            outputs=[
                user_input,
                function_choice,
                status_output,
                basic_info_output,
                knowledge_results_output,
                full_report_output
            ]
        )
    
    return demo


if __name__ == "__main__":
    print("🚀 启动 RNA 序列分析系统 Web 界面...")
    print("🌐 访问地址: http://127.0.0.1:7866")
    print("📱 支持移动端访问")
    
    demo = create_interface()
    demo.launch(
        server_name="127.0.0.1",
        server_port=7866,
        share=False,
        show_error=True,
        quiet=False
    )
