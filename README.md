# 🧬 RNA 序列分析系统

基于 LangChain 和智谱AI GLM-4 的智能RNA序列分析平台，支持自然语言输入、知识库检索和专业分析报告生成。


## ✨ 核心特性

### 🤖 双Agent智能架构
- **SequenceParserAgent**: 智能序列解析，支持自然语言到RNA序列的转换
- **RAGAgent**: 知识库检索，基于29个PDF文档的专业知识库

### 🧠 智谱AI GLM-4 驱动
- **智能序列提取**: 理解复杂自然语言描述中的序列信息
- **专业分析报告**: 生成详细的生物信息学分析和优化建议
- **中文优化**: 原生中文支持，理解更准确

### 🌐 多种使用方式
- **命令行界面**: `main.py` - 标准分析流程
- **Web界面**: `rna_gradio_app.py` - 用户友好的网页界面
- **流式界面**: `streaming_gradio_app.py` - 实时显示分析过程

### 📚 专业知识库
- **29个PDF文档**: 涵盖RNA设计、优化、分析等领域
- **ChromaDB向量数据库**: 高效的语义搜索
- **3427个文档块**: 详细的知识片段检索

### 🔧 可扩展架构
- **本地函数框架**: 支持自定义序列处理函数
- **模块化设计**: 易于扩展和维护
- **完善的错误处理**: 稳定可靠的系统运行

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
cd rag_agents_langchain

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API

创建 `.env` 文件并配置智谱AI API：

```env
# 智谱AI GLM-4 配置
API_KEY=your_zhipuai_api_key_here
BASE_URL=https://open.bigmodel.cn/api/paas/v4/
MODEL_NAME=glm-4
```

### 3. 启动系统

```bash
# 🌐 Web界面 (推荐)
python rna_gradio_app.py
# 访问: http://127.0.0.1:7865

# 🌊 流式界面 (实时显示分析过程)
python streaming_gradio_app.py  
# 访问: http://127.0.0.1:7899

# 💻 命令行界面
python main.py
```

## 📖 使用示例

### 输入示例
```
我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下
```

### 分析流程
1. **🔍 序列解析**: 智能提取RNA序列 `ACCGGGCCCUUUGUA`
2. **⚙️ 本地处理**: 执行序列分析函数
3. **📚 知识检索**: 搜索相关文献资料
4. **🧠 智能分析**: 生成专业分析报告

### 输出结果
- **序列信息**: 提取的RNA序列、类型、长度
- **本地分析**: 序列特征、指标计算
- **知识库检索**: 相关文献和研究
- **专业建议**: 序列优化、实验建议、文献支持

## 🏗️ 系统架构

```
用户输入 (自然语言)
    ↓
SequenceParserAgent (智谱AI GLM-4)
    ↓
本地函数处理 (可扩展)
    ↓  
RAGAgent (知识库检索)
    ↓
智能分析报告 (智谱AI GLM-4)
    ↓
结果展示 (Web/命令行)
```

### 核心组件

- **`main.py`**: 主协调器，标准分析流程
- **`streaming_orchestrator.py`**: 流式协调器，实时分析过程
- **`agents/`**: Agent模块
  - `sequence_parser/`: 序列解析Agent
  - `rag_agent/`: 知识库检索Agent
- **`utils/`**: 工具模块
  - `local_functions.py`: 本地函数框架
  - `sequence_processor.py`: 序列处理工具
- **`config/`**: 配置模块
- **`data/`**: 数据目录
  - `chroma_db/`: 向量数据库
  - `related_paper/`: PDF文档库

## 🎯 功能特色

### 🌊 流式分析
实时显示分析过程的每个步骤：
- **步骤 1/4**: 序列解析
- **步骤 2/4**: 本地函数处理  
- **步骤 3/4**: 知识库搜索
- **步骤 4/4**: 智能分析报告

### 🔧 自定义函数
在 `utils/local_functions.py` 中添加自定义序列处理函数：

```python
def _your_custom_function(self, sequence: str, **kwargs) -> Dict[str, Any]:
    """您的自定义函数"""
    # 实现您的序列处理逻辑
    processed_sequence = your_processing_logic(sequence)
    
    return {
        'output_sequence': processed_sequence,
        'metadata': {
            'operation': 'your_custom_function',
            'input_length': len(sequence),
            'output_length': len(processed_sequence),
        },
    }
```

### 📊 Web界面特性
- **📝 输入区域**: 自然语言输入 + 函数选择
- **📊 结果展示**: 多标签页显示（基本信息、知识库、完整报告）
- **💡 预设示例**: 快速开始的示例输入
- **📱 响应式设计**: 支持移动端访问

## 🧪 测试验证

### 系统测试
```bash
# 测试序列解析Agent
python agents/sequence_parser/sequence_parser_agent.py

# 测试本地函数
python utils/local_functions.py

# 测试流式分析
python streaming_orchestrator.py
```

### API测试
```bash
# 快速API连接测试
python quick_zhipuai_test.py

# 完整API功能测试  
python test_zhipuai_api.py
```

## 📁 项目结构

```
rag_agents_langchain/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── .env                        # API配置 (需要创建)
├── main.py                     # 主协调器
├── streaming_orchestrator.py   # 流式协调器
├── rna_gradio_app.py           # 标准Web界面
├── streaming_gradio_app.py     # 流式Web界面
├── agents/                     # Agent模块
│   ├── sequence_parser/        # 序列解析Agent
│   └── rag_agent/             # 知识库检索Agent
├── utils/                      # 工具模块
│   ├── local_functions.py      # 本地函数框架
│   ├── sequence_processor.py   # 序列处理工具
│   └── pdf_processor.py        # PDF处理工具
├── config/                     # 配置模块
│   └── api_config.py          # API配置
├── data/                       # 数据目录
│   ├── chroma_db/             # 向量数据库
│   └── related_paper/         # PDF文档库 (29个文档)
└── reports/                    # 分析报告输出
```

## 🔧 配置说明

### API配置 (.env)
```env
# 智谱AI GLM-4 配置
API_KEY=your_api_key_here
BASE_URL=https://open.bigmodel.cn/api/paas/v4/
MODEL_NAME=glm-4

# 可选配置
TEMPERATURE=0.3
MAX_TOKENS=2000
TIMEOUT=60
```

### 系统要求
- **Python**: 3.8+
- **内存**: 建议4GB+
- **存储**: 2GB+ (包含知识库)
- **网络**: 需要访问智谱AI API


