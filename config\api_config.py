#!/usr/bin/env python3
"""
API Configuration for LangChain Agents
配置 LangChain agents 使用的外部 API
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Custom API Configuration
CUSTOM_API_CONFIG = {
    'base_url': os.getenv('BASE_URL', ''),
    'model_name': os.getenv('MODEL_NAME', ''),
    'api_key': os.getenv('API_KEY'),
    'timeout': 30,
    'max_retries': 3,
    'temperature': 0.1,
    'max_tokens': 2048,
}

# LangChain LLM Configuration
LANGCHAIN_CONFIG = {
    'model_name': CUSTOM_API_CONFIG['model_name'],
    'openai_api_base': CUSTOM_API_CONFIG['base_url'],
    'openai_api_key': CUSTOM_API_CONFIG['api_key'],
    'temperature': CUSTOM_API_CONFIG['temperature'],
    'max_tokens': CUSTOM_API_CONFIG['max_tokens'],
    'timeout': CUSTOM_API_CONFIG['timeout'],
    'max_retries': CUSTOM_API_CONFIG['max_retries'],
}

def get_langchain_config() -> Dict[str, Any]:
    """获取 LangChain 配置"""
    return LANGCHAIN_CONFIG.copy();

def get_api_config() -> Dict[str, Any]:
    """获取 API 配置"""
    return CUSTOM_API_CONFIG.copy();

def validate_config() -> bool:
    """验证配置是否完整"""
    required_keys = ['base_url', 'api_key', 'model_name'];
    return all(CUSTOM_API_CONFIG.get(key) for key in required_keys);

if __name__ == '__main__':
    print('LangChain API Configuration:');
    print('=' * 40);
    config = get_langchain_config();
    for key, value in config.items():
        if key == 'openai_api_key' and value:
            print(f'{key}: {"*" * len(value)}');  # Hide API key
        else:
            print(f'{key}: {value}');
    
    print(f'\nConfiguration Valid: {validate_config()}');
