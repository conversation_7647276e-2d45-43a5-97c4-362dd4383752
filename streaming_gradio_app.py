#!/usr/bin/env python3
"""
流式 RNA 序列分析系统 - Gradio Web 界面
支持实时显示分析过程
"""

import sys
import os
import gradio as gr
import traceback
import time
from datetime import datetime
from typing import Generator, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 全局变量
orchestrator = None
system_initialized = False
init_error = None


def initialize_system():
    """初始化系统"""
    global orchestrator, system_initialized, init_error

    if system_initialized:
        return True

    try:
        from streaming_orchestrator import StreamingRNAAnalysisOrchestrator
        from config.api_config import validate_config

        if not validate_config():
            init_error = "❌ API 配置不完整，请检查 .env 文件"
            return False

        orchestrator = StreamingRNAAnalysisOrchestrator()
        system_initialized = True
        return True

    except Exception as e:
        init_error = f"❌ 系统初始化失败: {str(e)}"
        return False


def stream_analysis(user_input: str, function_name: str):
    """流式分析 RNA 序列"""

    # 检查输入
    if not user_input or not user_input.strip():
        yield "⚠️ 请输入包含 RNA 序列的描述", "", "", ""
        return

    # 初始化系统
    yield "🔧 正在初始化系统...", "", "", ""
    if not initialize_system():
        yield init_error, "", "", ""
        return

    try:
        # 使用流式协调器进行分析
        basic_info = ""
        knowledge_info = ""
        full_report = ""

        for step, report in orchestrator.stream_analyze_sequence(user_input, function_name):
            # 更新状态信息
            status_msg = f"[{step.step_number}/4] {step.step_name}: {step.status}\n{step.message}"

            if step.status == "failed":
                yield f"❌ {status_msg}", basic_info, knowledge_info, full_report
                return

            # 根据步骤更新相应的信息
            if step.step_number == 1 and step.status == "completed":
                # 序列解析完成，更新基本信息
                basic_info = f"""
## 📊 序列提取结果

- **提取序列**: `{report.extracted_sequence}`
- **序列类型**: {report.sequence_type or '未知'}
- **序列长度**: {len(report.extracted_sequence)} 个碱基
- **提取置信度**: {report.confidence_score:.2f}
"""

            elif step.step_number == 2 and step.status == "completed":
                # 本地函数处理完成，更新基本信息
                local_result = report.local_function_result
                basic_info += f"""

## 🔬 本地函数处理结果

- **执行函数**: {local_result['function_name']}
- **执行状态**: ✅ 成功
- **执行时间**: {local_result['execution_time']:.4f} 秒

**详细结果**:
"""
                if local_result['metadata']:
                    for key, value in local_result['metadata'].items():
                        if isinstance(value, float):
                            basic_info += f"- **{key}**: {value:.3f}\n"
                        else:
                            basic_info += f"- **{key}**: {value}\n"

            elif step.step_number == 3 and step.status == "completed":
                # 知识库搜索完成，更新知识库信息
                knowledge_results = report.knowledge_search_results
                total_docs = sum(result['documents_found'] for result in knowledge_results.values())

                knowledge_info = f"## 📚 知识库搜索结果\n\n**总找到文档数**: {total_docs} 篇\n\n"

                for query_name, result in knowledge_results.items():
                    knowledge_info += f"### {query_name.replace('_', ' ').title()}\n"
                    knowledge_info += f"- **查询**: {result['query']}\n"
                    knowledge_info += f"- **找到文档**: {result['documents_found']} 篇\n"

                    if result['documents']:
                        knowledge_info += "- **相关文档**:\n"
                        for j, doc in enumerate(result['documents'][:2], 1):  # 只显示前2个
                            knowledge_info += f"  {j}. **{doc['title']}**\n"
                            knowledge_info += f"     - 来源: {doc['source']}\n"
                    knowledge_info += "\n"

            elif step.step_number == 4 and step.status == "completed":
                # 最终分析完成，更新完整报告
                full_report = report.final_analysis or "报告生成中..."

                # 最终状态信息
                final_status = f"""✅ 分析完成！

🧬 **提取序列**: {report.extracted_sequence}
🏷️ **序列类型**: {report.sequence_type}
📏 **序列长度**: {len(report.extracted_sequence)} bp
🎯 **提取置信度**: {report.confidence_score:.2f}
🎯 **整体置信度**: {report.overall_confidence:.2f}
⚙️ **本地函数**: {report.local_function_result['function_name']}
📚 **相关文档**: {sum(r['documents_found'] for r in report.knowledge_search_results.values())} 篇
⏱️ **分析时间**: {report.timestamp.strftime('%H:%M:%S')}"""

                yield final_status, basic_info, knowledge_info, full_report
                return

            # 输出当前步骤状态
            yield status_msg, basic_info, knowledge_info, full_report
        
    except Exception as e:
        error_msg = f"❌ 分析过程中出现错误: {str(e)}"
        error_detail = f"错误详情:\n{traceback.format_exc()}"
        yield error_msg, error_detail, "", ""


# 预定义示例
EXAMPLES = [
    ["我的 mRNA 是针对 NGF 优化的 5'UTR，ACCGGGCCCTTTGTA，你评价下", "example_function"],
    ["请分析这个编码序列：AUGCCCGGGAAAUUUCCCGGGCAU，这是一个CDS区域", "example_function"],
    ["这是一个RNA序列：AUGAAACCCGGGUUUAAACCCUAA，请分析一下", "example_function"],
    ["分析这个5'UTR序列：GGGCCCAAAUUUGGGCCC", "example_function"]
]

# 函数选项
FUNCTION_OPTIONS = [
    ("示例函数", "example_function"),
]


def create_streaming_interface():
    """创建流式 Gradio 界面"""
    
    with gr.Blocks(
        title="RNA 序列分析系统 - 流式版",
        theme=gr.themes.Soft(),
        css="""
        .main-header {
            text-align: center;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .status-box {
            background: #f0f8ff;
            border-left: 4px solid #4CAF50;
            padding: 10px;
            margin: 10px 0;
        }
        """
    ) as demo:
        
        # 标题
        gr.HTML("""
        <div class="main-header">
            <h1>🧬 RNA 序列分析系统 - 流式版</h1>
            <h3>实时显示分析过程的智能生物信息学分析平台</h3>
            <p>🔄 流式处理 | 🤖 双Agent架构 | 📚 知识库检索 | 🧠 智能分析</p>
        </div>
        """)
        
        with gr.Row():
            # 左侧输入区域
            with gr.Column(scale=1):
                gr.Markdown("## 📝 输入区域")
                
                user_input = gr.Textbox(
                    label="RNA 序列描述",
                    placeholder="请输入包含 RNA 序列的自然语言描述...",
                    lines=5,
                    max_lines=10
                )
                
                function_choice = gr.Dropdown(
                    choices=FUNCTION_OPTIONS,
                    value="example_function",
                    label="选择分析函数",
                    info="选择要执行的本地序列处理函数"
                )
                
                with gr.Row():
                    analyze_btn = gr.Button("🚀 开始流式分析", variant="primary", size="lg")
                    stop_btn = gr.Button("⏹️ 停止", variant="stop", size="lg")
                
                # 示例
                gr.Markdown("## 💡 使用示例")
                gr.Examples(
                    examples=EXAMPLES,
                    inputs=[user_input, function_choice],
                    label="点击示例快速开始"
                )
            
            # 右侧结果区域
            with gr.Column(scale=2):
                gr.Markdown("## 📊 实时分析过程")
                
                # 实时状态显示
                status_output = gr.Textbox(
                    label="🔄 分析状态 (实时更新)",
                    lines=6,
                    interactive=False,
                    elem_classes=["status-box"]
                )
                
                # 结果标签页
                with gr.Tabs():
                    with gr.TabItem("📊 基本信息"):
                        basic_info_output = gr.Markdown()
                    
                    with gr.TabItem("📚 知识库"):
                        knowledge_results_output = gr.Markdown()
                    
                    with gr.TabItem("📋 完整报告"):
                        full_report_output = gr.Markdown()
        
        # 系统信息
        with gr.Accordion("ℹ️ 流式分析说明", open=False):
            gr.Markdown("""
            ### 🔄 流式分析特点
            - **实时反馈**: 每个分析步骤都会实时显示进度
            - **透明过程**: 用户可以看到完整的分析流程
            - **即时结果**: 每个步骤完成后立即显示结果
            - **错误处理**: 出现问题时立即反馈给用户
            
            ### 📋 分析步骤
            1. **步骤 1/4**: 解析自然语言并提取 RNA 序列
            2. **步骤 2/4**: 执行本地函数处理
            3. **步骤 3/4**: 搜索相关知识库文献
            4. **步骤 4/4**: 生成智能分析报告
            
            ### 💡 使用提示
            - 分析过程中可以随时查看各个标签页的结果
            - 状态框会实时显示当前进行的步骤
            - 如需停止分析，点击"停止"按钮
            """)
        
        # 事件绑定 - 流式输出
        analyze_btn.click(
            fn=stream_analysis,
            inputs=[user_input, function_choice],
            outputs=[
                status_output,
                basic_info_output,
                knowledge_results_output,
                full_report_output
            ],
            show_progress=True
        )
    
    return demo


if __name__ == "__main__":
    print("🚀 启动流式 RNA 序列分析系统...")
    print("🌐 访问地址: http://127.0.0.1:7899")
    print("🔄 支持实时流式分析过程展示")
    
    demo = create_streaming_interface()
    demo.launch(
        server_name="127.0.0.1",
        server_port=7899,
        share=False,
        show_error=True,
        quiet=False
    )
