#!/usr/bin/env python3
"""
RAG Agent
使用知识库查询相关文章的 LangChain Agent
"""

import os;
from typing import Dict, List, Any, Optional;
from pydantic import BaseModel, Field;
from langchain.agents import AgentExecutor, create_openai_tools_agent;
from langchain.tools import BaseTool, tool;
from langchain.schema import BaseMessage;
from langchain_openai import ChatOpenAI;
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder;
from langchain_community.vectorstores import Chroma;
from langchain_community.embeddings import SentenceTransformerEmbeddings;
from langchain.schema import Document;
from langchain.text_splitter import RecursiveCharacterTextSplitter;

from config.api_config import get_langchain_config;
from utils.pdf_processor import PDFProcessor;


class KnowledgeSearchResult(BaseModel):
    """知识库搜索结果"""
    query: str = Field(description='查询内容');
    documents: List[Document] = Field(description='相关文档');
    relevance_scores: List[float] = Field(description='相关性分数');
    summary: Optional[str] = Field(description='结果摘要');


def create_knowledge_search_tool(vector_store):
    """创建知识库搜索工具"""

    @tool
    def search_knowledge_base(query: str, top_k: int = 5) -> str:
        """在知识库中搜索与给定序列相关的文章和资料"""
        try:
            # 搜索相关文档
            docs = vector_store.similarity_search(query, k=top_k);

            if not docs:
                return '未找到相关文档';

            # 格式化结果
            result = f'找到 {len(docs)} 篇相关文档:\n\n';
            for i, doc in enumerate(docs, 1):
                result += f'{i}. {doc.metadata.get("title", "无标题")}\n';
                result += f'   内容: {doc.page_content[:200]}...\n';
                result += f'   来源: {doc.metadata.get("source", "未知")}\n\n';

            return result;

        except Exception as e:
            return f'搜索出错: {str(e)}';

    return search_knowledge_base;


class RAGAgent:
    """RAG Agent"""
    
    def __init__(self, knowledge_base_path: str = './data', test_mode: bool = False):
        self.config = get_langchain_config();
        self.test_mode = test_mode;

        # 首先初始化向量存储和工具
        self.vector_store = self._initialize_vector_store(knowledge_base_path);
        self.tools = [create_knowledge_search_tool(self.vector_store)];

        if test_mode:
            # 测试模式：不初始化 LLM 和 Agent
            self.llm = None;
            self.agent_executor = None;
        else:
            # 正常模式：初始化 LLM 和 Agent
            self.llm = self._initialize_llm();
            self.agent_executor = self._create_agent();
    
    def _initialize_llm(self):
        """初始化 LLM"""
        return ChatOpenAI(
            model_name=self.config['model_name'],
            openai_api_base=self.config['openai_api_base'],
            openai_api_key=self.config['openai_api_key'],
            temperature=self.config['temperature'],
            max_tokens=self.config['max_tokens'],
            timeout=self.config['timeout'],
            max_retries=self.config['max_retries'],
        );
    
    def _initialize_vector_store(self, knowledge_base_path: str):
        """初始化向量存储"""
        try:
            # 尝试加载现有的向量存储
            persist_directory = os.path.join(knowledge_base_path, 'chroma_db');
            if os.path.exists(persist_directory):
                try:
                    embeddings = SentenceTransformerEmbeddings(
                        model_name='sentence-transformers/all-MiniLM-L6-v2',
                    );
                    vector_store = Chroma(
                        persist_directory=persist_directory,
                        embedding_function=embeddings,
                    );
                    print(f'已加载现有的向量存储: {persist_directory}');
                    return vector_store;
                except Exception as e:
                    print(f'加载现有向量存储失败: {e}');
        
            # 创建新的向量存储
            print('创建新的向量存储...');
            return self._create_sample_knowledge_base(knowledge_base_path);
            
        except Exception as e:
            print(f'向量存储初始化失败: {e}');
            # 返回一个简单的模拟向量存储
            return self._create_mock_vector_store();
    
    def _create_sample_knowledge_base(self, knowledge_base_path: str):
        """创建基于 PDF 的真实知识库"""
        try:
            print('正在从 PDF 文件创建知识库...');
            
            # 使用 PDF 处理器提取文档
            pdf_processor = PDFProcessor();
            pdf_directory = os.path.join(knowledge_base_path, 'related_paper');
            
            if not os.path.exists(pdf_directory):
                print(f'PDF 目录不存在: {pdf_directory}');
                return self._create_sample_docs_fallback();
            
            # 处理 PDF 文件
            pdf_documents = pdf_processor.process_pdf_directory(pdf_directory);
            
            if not pdf_documents:
                print('没有成功处理任何 PDF 文件，使用示例文档');
                return self._create_sample_docs_fallback();
            
            # 转换为 LangChain Document 格式
            langchain_docs = [];
            for pdf_doc in pdf_documents:
                # 将长文档分块
                text_chunks = self._split_text_into_chunks(pdf_doc.content);
                
                for i, chunk in enumerate(text_chunks):
                    langchain_docs.append(Document(
                        page_content=chunk,
                        metadata={
                            'title': pdf_doc.title,
                            'source': pdf_doc.metadata['file_name'],
                            'type': 'pdf_research',
                            'page_count': pdf_doc.page_count,
                            'word_count': pdf_doc.word_count,
                            'chunk_index': i,
                            'total_chunks': len(text_chunks),
                        },
                    ));
            
            print(f'从 {len(pdf_documents)} 个 PDF 文件创建了 {len(langchain_docs)} 个文档块');
            
            # 尝试创建嵌入和向量存储
            try:
                embeddings = SentenceTransformerEmbeddings(
                    model_name='sentence-transformers/all-MiniLM-L6-v2',
                );
                
                persist_directory = os.path.join(knowledge_base_path, 'chroma_db');
                os.makedirs(persist_directory, exist_ok=True);
                
                vector_store = Chroma.from_documents(
                    documents=langchain_docs,
                    embedding=embeddings,
                    persist_directory=persist_directory,
                );
                
                # 持久化向量存储
                vector_store.persist();
                print(f'已创建基于 PDF 的知识库: {persist_directory}');
                
                return vector_store;
                
            except Exception as e:
                print(f'创建真实向量存储失败，使用模拟存储: {e}');
                return self._create_mock_vector_store();
                
        except Exception as e:
            print(f'创建 PDF 知识库失败，使用模拟存储: {e}');
            return self._create_mock_vector_store();
    
    def _split_text_into_chunks(self, text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """将文本分割为块"""
        if len(text) <= chunk_size:
            return [text];
        
        chunks = [];
        start = 0;
        
        while start < len(text):
            end = start + chunk_size;
            
            # 如果不是最后一块，尝试在句子边界分割
            if end < len(text):
                # 寻找最近的句号、问号或感叹号
                for i in range(end, start + chunk_size // 2, -1):
                    if text[i] in '.!?':
                        end = i + 1;
                        break;
            
            chunk = text[start:end].strip();
            if chunk:
                chunks.append(chunk);
            
            start = end - chunk_overlap;
        
        return chunks;
    
    def _create_sample_docs_fallback(self):
        """创建示例文档作为备用方案"""
        sample_docs = [
            Document(
                page_content='''
                NGF (Nerve Growth Factor) 是神经生长因子，在神经发育和再生中起重要作用。
                NGF 的 mRNA 序列优化对于提高蛋白质表达效率至关重要。
                5'UTR 序列的设计可以显著影响翻译效率和蛋白质产量。
                ''',
                metadata={'title': 'NGF mRNA 优化研究', 'source': '文献1', 'type': 'research'},
            ),
            Document(
                page_content='''
                RNA 序列设计中的关键要素包括：
                1. 5'UTR 序列的优化
                2. 密码子使用偏好
                3. 二级结构预测
                4. 翻译起始位点的设计
                ACCGGGCCCTTTGTA 是一个典型的 5'UTR 序列模式。
                ''',
                metadata={'title': 'RNA 序列设计原则', 'source': '文献2', 'type': 'guide'},
            ),
        ];
        
        try:
            embeddings = SentenceTransformerEmbeddings(
                model_name='sentence-transformers/all-MiniLM-L6-v2',
            );
            
            persist_directory = os.path.join('./data', 'chroma_db');
            os.makedirs(persist_directory, exist_ok=True);
            
            vector_store = Chroma.from_documents(
                documents=sample_docs,
                embedding=embeddings,
                persist_directory=persist_directory,
            );
            
            vector_store.persist();
            print(f'已创建示例知识库: {persist_directory}');
            
            return vector_store;
            
        except Exception as e:
            print(f'创建示例知识库失败，使用模拟存储: {e}');
            return self._create_mock_vector_store();
    
    def _create_mock_vector_store(self):
        """创建模拟向量存储（用于测试）"""
        class MockVectorStore:
            def similarity_search(self, query: str, k: int = 5):
                # 返回模拟的搜索结果
                return [
                    Document(
                        page_content=f'模拟搜索结果 1: {query} 相关的研究内容...',
                        metadata={'title': f'{query} 研究', 'source': '模拟文献1', 'type': 'research'},
                    ),
                    Document(
                        page_content=f'模拟搜索结果 2: 关于 {query} 的最新发现...',
                        metadata={'title': f'{query} 最新进展', 'source': '模拟文献2', 'type': 'research'},
                    ),
                ][:k];
            
            def persist(self):
                pass;
        
        print('创建了模拟向量存储（用于测试）');
        return MockVectorStore();
    
    def _create_agent(self):
        """创建 Agent"""
        # 定义提示模板
        prompt = ChatPromptTemplate.from_messages([
            ('system', '''你是一个专业的 RNA 研究专家。你的任务是：

1. 根据给定的 RNA 序列或相关描述，在知识库中搜索相关的研究文章和资料
2. 深入分析搜索结果的相关性和重要性
3. 提供基于文献的专业见解和科学建议
4. 总结关键发现和推荐具体的优化方向

请使用 search_knowledge_base 工具来搜索相关文档，然后基于你的专业知识深入分析结果。

搜索策略：
- 使用序列本身作为关键词
- 使用序列类型（如 5UTR、mRNA）作为关键词
- 使用相关的生物学概念（如 NGF、神经生长因子）作为关键词
- 结合多个搜索词以获得更全面的结果

分析要求：
- 评估文献的相关性和可信度
- 提取关键的科学发现
- 结合当前研究趋势提供建议
- 指出潜在的研究空白和机会

请始终提供基于文献的、有科学依据的深入分析。'''),
            ('human', '{input}'),
            MessagesPlaceholder(variable_name='agent_scratchpad'),
        ]);
        
        # 创建 Agent
        agent = create_openai_tools_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt,
        );
        
        # 创建 Agent 执行器
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3,
        );
        
        return agent_executor;
    
    def search_knowledge(self, query: str, top_k: int = 5) -> KnowledgeSearchResult:
        """搜索知识库"""
        try:
            # 直接搜索向量存储获取文档
            docs = self.vector_store.similarity_search(query, k=top_k);

            # 计算相关性分数（简化版本）
            relevance_scores = [0.9 - i * 0.1 for i in range(len(docs))];

            # 如果有 agent_executor，使用它生成摘要
            summary = '';
            if self.agent_executor:
                try:
                    result = self.agent_executor.invoke({
                        'input': f'请搜索与以下内容相关的文献资料：{query}',
                    });
                    summary = result.get('output', '');
                except Exception as e:
                    summary = f'Agent 分析失败: {str(e)}';
            else:
                # 生成简单摘要
                summary = f'找到 {len(docs)} 篇与 "{query}" 相关的文档';

            return KnowledgeSearchResult(
                query=query,
                documents=docs,
                relevance_scores=relevance_scores,
                summary=summary,
            );

        except Exception as e:
            return KnowledgeSearchResult(
                query=query,
                documents=[],
                relevance_scores=[],
                summary=f'搜索出错: {str(e)}',
            );
    
    def add_documents(self, documents: List[Document]):
        """添加文档到知识库"""
        try:
            # 将新文档添加到向量存储
            self.vector_store.add_documents(documents);
            self.vector_store.persist();
            print(f'已添加 {len(documents)} 个文档到知识库');
        except Exception as e:
            print(f'添加文档失败: {e}');


# 使用示例
if __name__ == '__main__':
    agent = RAGAgent();
    
    test_query = 'NGF mRNA 5UTR 序列优化';
    result = agent.search_knowledge(test_query);
    
    print('知识库搜索结果:');
    print(f'查询: {result.query}');
    print(f'找到文档数: {len(result.documents)}');
    print(f'摘要: {result.summary}');
    
    for i, doc in enumerate(result.documents):
        print(f'\n文档 {i+1}:');
        print(f'标题: {doc.metadata.get("title", "无标题")}');
        print(f'内容: {doc.page_content[:100]}...');
