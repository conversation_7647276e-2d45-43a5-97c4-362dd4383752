#!/usr/bin/env python3
"""
Local Sequence Functions
本地序列处理函数
"""

import os;
import json;
from typing import Dict, List, Any, Optional, Callable;
from pydantic import BaseModel, Field;
from datetime import datetime;

# Import moved to avoid circular import issues when running as main


class LocalFunctionResult(BaseModel):
    """本地函数执行结果"""
    input_sequence: str = Field(description='输入序列');
    output_sequence: Optional[str] = Field(description='输出序列');
    function_name: str = Field(description='函数名称');
    execution_time: float = Field(description='执行时间（秒）');
    success: bool = Field(description='是否成功');
    error_message: Optional[str] = Field(description='错误信息');
    metadata: Dict[str, Any] = Field(description='元数据');


class LocalSequenceFunction:
    """本地序列处理函数管理器"""

    def __init__(self):
        try:
            from .sequence_processor import SequenceProcessor;
        except ImportError:
            from utils.sequence_processor import SequenceProcessor;
        self.processor = SequenceProcessor();
        self.functions = self._register_functions();
        self.results_cache = {};

    def _register_functions(self) -> Dict[str, Callable]:
        """注册本地函数"""
        return {
            'example_function': self._example_function,
        };
    
    def execute_function(self, function_name: str, sequence: str, **kwargs) -> LocalFunctionResult:
        """执行本地函数"""
        start_time = datetime.now();
        
        try:
            if function_name not in self.functions:
                raise ValueError(f'未知函数: {function_name}');
            
            # 验证序列
            clean_sequence = self.processor._clean_sequence(sequence);
            if not clean_sequence:
                raise ValueError('无效的 RNA 序列');
            
            # 执行函数
            function = self.functions[function_name];
            result = function(clean_sequence, **kwargs);
            
            execution_time = (datetime.now() - start_time).total_seconds();
            
            return LocalFunctionResult(
                input_sequence=clean_sequence,
                output_sequence=result.get('output_sequence'),
                function_name=function_name,
                execution_time=execution_time,
                success=True,
                error_message=None,
                metadata=result.get('metadata', {}),
            );
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds();
            
            return LocalFunctionResult(
                input_sequence=sequence,
                output_sequence=None,
                function_name=function_name,
                execution_time=execution_time,
                success=False,
                error_message=str(e),
                metadata={},
            );
    
    def _example_function(self, sequence: str, **kwargs) -> Dict[str, Any]:
        """序列优化分析函数 - 分析原始序列和优化后的序列"""
        try:
            # 读取优化结果数据
            import pandas as pd
            import os

            # 尝试读取CSV文件
            csv_file = 'best_10_utr5_ngf.csv'
            if not os.path.exists(csv_file):
                # 如果文件不存在，使用您提供的示例数据
                optimization_data = self._get_sample_optimization_data()
            else:
                df = pd.read_csv(csv_file)
                optimization_data = df.to_dict('records')

            # 分析数据结构
            if not optimization_data:
                raise ValueError("没有找到优化数据")

            # 找到原始序列（通常是 update=0, episode=0 的记录）
            original_sequence = None
            optimized_sequences = []

            for record in optimization_data:
                if record.get('update', -1) == 0 and record.get('episode', -1) == 0:
                    original_sequence = record
                else:
                    optimized_sequences.append(record)

            # 如果没有找到原始序列，使用最后一个记录
            if original_sequence is None and optimization_data:
                original_sequence = optimization_data[-1]
                optimized_sequences = optimization_data[:-1]

            # 找到最佳优化序列（score最低的）用于统计分析
            best_sequence = None
            if optimized_sequences:
                best_sequence = min(optimized_sequences, key=lambda x: x.get('score', float('inf')))

            # 计算改进统计
            improvement_stats = self._calculate_improvement_stats(original_sequence, optimized_sequences)

            # 返回所有序列作为输出（原始序列 + 所有优化序列）
            all_sequences = []
            if original_sequence:
                all_sequences.append(original_sequence)
            all_sequences.extend(optimized_sequences)

            # 将所有序列格式化为字符串输出
            output_sequence = self._format_all_sequences(all_sequences)

            return {
                'output_sequence': output_sequence,
                'metadata': {
                    'operation': 'sequence_optimization_analysis',
                    'input_sequence': sequence,
                    'input_length': len(sequence),
                    'output_length': len(output_sequence),
                    'original_sequence_info': original_sequence,
                    'best_optimized_sequence': best_sequence,
                    'all_sequences': all_sequences,
                    'total_sequences': len(all_sequences),
                    'total_optimized_variants': len(optimized_sequences),
                    'improvement_stats': improvement_stats,
                    'analysis_summary': self._generate_analysis_summary(original_sequence, best_sequence, improvement_stats),
                    'sequences_summary': self._generate_sequences_summary(all_sequences),
                },
            }

        except Exception as e:
            # 如果出错，返回基本的序列分析
            return self._fallback_analysis(sequence, str(e))

    def _get_sample_optimization_data(self) -> List[Dict[str, Any]]:
        """获取示例优化数据（基于您提供的数据）"""
        return [
            {
                'update': 0, 'episode': 0,
                'sequence': 'GCTCCGGCACAGCAGAGAGCGCTGGGAGCCGGAGGGGAGCGCAGCGAGTTTTGGCCAGTGGTCGTGCAGTCCAAGGGGCTGGATGGCATGCTGGACCCAAGCTCAGCTCAGCGTCCGGACCCAATAACAGTTTTACCAAGGGAGCAGCTTTCTATCCTGGCCACACTGAGGTGCATAGCGTA',
                'd1': 2.44, 'd2': 4.22, 'd3': -74.0, 'd4': 0.0, 'score': 0.0
            },
            {
                'update': 76, 'episode': 10288,
                'sequence': 'GCTCCGGCGCAGCAGAGAGCGCTGGGAGCCGGAGGGGAGCGCAGCGAGTTTTGGCCGGTGGTCGTGCAGTCCAAGGGGCCGGCTGGCATGCTGGAGCCAAGCTCAGCTCAGCGTCCGGACCCAATCACAGCTTCACCAAGGGGGCAGCTTTCTATGCTGGCCACACTGAGGAGCATAGCGTA',
                'd1': 2.761439561843872, 'd2': 4.592376232147217, 'd3': -77.9000015258789, 'd4': 0.0631586591539429, 'score': -32.54623954495478
            },
            {
                'update': 105, 'episode': 14334,
                'sequence': 'GCTCCGGCACAGCAGAGAGCGCTGGGAGCCGGAGGGGAGCGCAGCGAGGTTTGGCCGGTGCTCGTGCAGGCCAAGGGGCCGGATGCCTGGCTGCACCCAAGCTCAGCTCAGCGTCCGGACCCAATAACAGCTTTACCAAGGGAGCAGCTTTCTATCCTGGCCTCACTGAGGTGCATAGCGCA',
                'd1': 2.8225221633911133, 'd2': 4.586204528808594, 'd3': -78.80000305175781, 'd4': 0.069014174040189, 'score': -34.90552244347715
            },
            {
                'update': 108, 'episode': 14711,
                'sequence': 'GCTCCGGCACTGCAGAGAGCGCTGGGAGCCGGAGGGGAGCGCAGCGGGTTTTGGCCGGTGGTCGTGCAGTCGAAGGGGCTGGATGGCATGCTGGACCCCAGCTCAGCTCAGCGTCCGGAGCCACTAACAGTTTTACCGAGGGAGCAGCTTTCTATCCTGGCCACACGCAGGTGCATAGCGGA',
                'd1': 2.770460367202759, 'd2': 4.5410237312316895, 'd3': -73.0, 'd4': 0.0630260785168312, 'score': -30.128233687070303
            }
        ]

    def _calculate_improvement_stats(self, original: Dict[str, Any], optimized_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算改进统计信息"""
        if not original or not optimized_list:
            return {}

        # 找到最佳序列
        best_seq = min(optimized_list, key=lambda x: x.get('score', float('inf')))

        # 计算改进幅度
        original_score = original.get('score', 0)
        best_score = best_seq.get('score', 0)
        score_improvement = abs(best_score - original_score)

        # 计算各指标的改进
        d1_improvement = best_seq.get('d1', 0) - original.get('d1', 0)
        d2_improvement = best_seq.get('d2', 0) - original.get('d2', 0)
        d3_improvement = best_seq.get('d3', 0) - original.get('d3', 0)
        d4_improvement = best_seq.get('d4', 0) - original.get('d4', 0)

        return {
            'score_improvement': score_improvement,
            'd1_improvement': d1_improvement,
            'd2_improvement': d2_improvement,
            'd3_improvement': d3_improvement,
            'd4_improvement': d4_improvement,
            'best_episode': best_seq.get('episode', 0),
            'best_update': best_seq.get('update', 0),
            'optimization_efficiency': score_improvement / max(1, best_seq.get('episode', 1))
        }

    def _generate_analysis_summary(self, original: Dict[str, Any], best: Dict[str, Any], stats: Dict[str, Any]) -> str:
        """生成分析摘要"""
        if not original or not best:
            return "无法生成分析摘要：缺少必要数据"

        summary = f"""
序列优化分析摘要：
• 原始评分: {original.get('score', 0):.2f}
• 最佳评分: {best.get('score', 0):.2f}
• 评分改进: {stats.get('score_improvement', 0):.2f}
• 最佳结果来自: Episode {best.get('episode', 0)}, Update {best.get('update', 0)}
• 优化效率: {stats.get('optimization_efficiency', 0):.6f} (改进/episode)

指标变化：
• d1: {original.get('d1', 0):.3f} → {best.get('d1', 0):.3f} (Δ{stats.get('d1_improvement', 0):+.3f})
• d2: {original.get('d2', 0):.3f} → {best.get('d2', 0):.3f} (Δ{stats.get('d2_improvement', 0):+.3f})
• d3: {original.get('d3', 0):.1f} → {best.get('d3', 0):.1f} (Δ{stats.get('d3_improvement', 0):+.1f})
• d4: {original.get('d4', 0):.3f} → {best.get('d4', 0):.3f} (Δ{stats.get('d4_improvement', 0):+.3f})
        """.strip()

        return summary

    def _fallback_analysis(self, sequence: str, error_msg: str) -> Dict[str, Any]:
        """备用分析方法"""
        return {
            'output_sequence': sequence,
            'metadata': {
                'operation': 'fallback_analysis',
                'input_length': len(sequence),
                'output_length': len(sequence),
                'error': error_msg,
                'message': '使用备用分析方法，因为无法读取优化数据',
                'basic_analysis': {
                    'gc_content': self._calculate_gc_content(sequence),
                    'length': len(sequence),
                    'has_start_codon': 'AUG' in sequence.upper(),
                    'has_stop_codon': any(stop in sequence.upper() for stop in ['UAA', 'UAG', 'UGA'])
                }
            }
        }

    def _calculate_gc_content(self, sequence: str) -> float:
        """计算GC含量"""
        if not sequence:
            return 0.0
        gc_count = sequence.upper().count('G') + sequence.upper().count('C')
        return gc_count / len(sequence)

    def _format_all_sequences(self, all_sequences: List[Dict[str, Any]]) -> str:
        """格式化所有序列为输出字符串"""
        if not all_sequences:
            return "未找到序列数据"

        formatted_output = []

        for i, seq_data in enumerate(all_sequences):
            seq_type = "原始序列" if (seq_data.get('update', -1) == 0 and seq_data.get('episode', -1) == 0) else f"优化序列 {i}"

            seq_info = f"""
{seq_type}:
  序列: {seq_data.get('sequence', 'N/A')}
  评分: {seq_data.get('score', 0):.2f}
  Episode: {seq_data.get('episode', 0)}
  Update: {seq_data.get('update', 0)}
  指标: d1={seq_data.get('d1', 0):.3f}, d2={seq_data.get('d2', 0):.3f}, d3={seq_data.get('d3', 0):.1f}, d4={seq_data.get('d4', 0):.3f}
            """.strip()

            formatted_output.append(seq_info)

        return "\n\n".join(formatted_output)

    def _generate_sequences_summary(self, all_sequences: List[Dict[str, Any]]) -> str:
        """生成所有序列的摘要"""
        if not all_sequences:
            return "无序列数据"

        # 统计信息
        total_count = len(all_sequences)
        original_count = sum(1 for seq in all_sequences if seq.get('update', -1) == 0 and seq.get('episode', -1) == 0)
        optimized_count = total_count - original_count

        # 评分统计
        scores = [seq.get('score', 0) for seq in all_sequences if seq.get('score') is not None]
        if scores:
            min_score = min(scores)
            max_score = max(scores)
            avg_score = sum(scores) / len(scores)
        else:
            min_score = max_score = avg_score = 0

        # 找到最佳和最差序列
        best_seq = min(all_sequences, key=lambda x: x.get('score', float('inf'))) if all_sequences else None
        worst_seq = max(all_sequences, key=lambda x: x.get('score', float('-inf'))) if all_sequences else None

        summary = f"""
所有序列统计摘要：
• 总序列数: {total_count}
• 原始序列: {original_count} 个
• 优化序列: {optimized_count} 个

评分统计：
• 最佳评分: {min_score:.2f} (Episode {best_seq.get('episode', 0) if best_seq else 0})
• 最差评分: {max_score:.2f} (Episode {worst_seq.get('episode', 0) if worst_seq else 0})
• 平均评分: {avg_score:.2f}
• 评分范围: {max_score - min_score:.2f}

优化效果：
• 评分改进: {max_score - min_score:.2f} 分
• 改进率: {((max_score - min_score) / abs(max_score) * 100) if max_score != 0 else 0:.1f}%
        """.strip()

        return summary

    def list_available_functions(self) -> List[str]:
        """列出可用的函数"""
        return list(self.functions.keys());

    def get_function_info(self, function_name: str) -> Dict[str, Any]:
        """获取函数信息"""
        if function_name not in self.functions:
            return {'error': f'未知函数: {function_name}'};

        # 函数描述
        descriptions = {
            'example_function': 'RNA序列优化分析 - 分析原始序列与优化后序列的性能差异，提供详细的改进统计',
        };

        return {
            'name': function_name,
            'description': descriptions.get(function_name, '无描述'),
            'parameters': [],  # 简化版本，实际可以更详细
        };


# 使用示例
if __name__ == '__main__':
    import sys;
    import os;
    # 添加项目根目录到路径
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))));

    local_func = LocalSequenceFunction();

    test_sequence = 'ACCGGGCCCTTTGTA';

    print('可用函数:');
    for func_name in local_func.list_available_functions():
        print(f'- {func_name}');

    print(f'\n测试序列: {test_sequence}');

    # 测试示例函数
    functions_to_test = ['example_function'];

    for func_name in functions_to_test:
        result = local_func.execute_function(func_name, test_sequence);
        print(f'\n{func_name}:');
        print(f'  成功: {result.success}');
        print(f'  输出: {result.output_sequence}');
        print(f'  执行时间: {result.execution_time:.4f}s');
        print(f'  元数据: {result.metadata}');
        if result.error_message:
            print(f'  错误: {result.error_message}');

