#!/usr/bin/env python3
"""
PDF Processor
PDF 文件处理模块，用于从 PDF 文件中提取文本内容
"""

import os;
import re;
from typing import List, Dict, Any, Optional;
from pathlib import Path;
from pydantic import BaseModel, Field;

try:
    import fitz;  # PyMuPDF
    PYMUPDF_AVAILABLE = True;
except ImportError:
    PYMUPDF_AVAILABLE = False;

try:
    import PyPDF2;
    PYPDF2_AVAILABLE = True;
except ImportError:
    PYPDF2_AVAILABLE = False;


class PDFDocument(BaseModel):
    """PDF 文档信息"""
    file_path: str = Field(description='PDF 文件路径');
    title: str = Field(description='文档标题');
    content: str = Field(description='提取的文本内容');
    page_count: int = Field(description='页面数量');
    word_count: int = Field(description='字数统计');
    metadata: Dict[str, Any] = Field(description='文档元数据');


class PDFProcessor:
    """PDF 处理器"""
    
    def __init__(self):
        self.supported_formats = ['.pdf'];
        self.text_cleanup_patterns = [
            (r'\s+', ' '),  # 多个空白字符替换为单个空格
            (r'\n\s*\n', '\n\n'),  # 多个换行替换为双换行
            (r'[^\w\s\n\.\,\;\:\!\?\(\)\[\]\-\+\=\%\$\@\#]', ''),  # 移除特殊字符
        ];
    
    def process_pdf_directory(self, directory_path: str) -> List[PDFDocument]:
        """处理目录中的所有 PDF 文件"""
        pdf_documents = [];
        directory = Path(directory_path);
        
        if not directory.exists():
            print(f'目录不存在: {directory_path}');
            return pdf_documents;
        
        pdf_files = list(directory.glob('*.pdf'));
        print(f'找到 {len(pdf_files)} 个 PDF 文件');
        
        for pdf_file in pdf_files:
            try:
                print(f'正在处理: {pdf_file.name}');
                document = self.process_pdf_file(str(pdf_file));
                if document:
                    pdf_documents.append(document);
                    print(f'  ✅ 成功提取 {document.word_count} 字');
                else:
                    print(f'  ❌ 处理失败');
            except Exception as e:
                print(f'  ❌ 处理 {pdf_file.name} 时出错: {str(e)}');
        
        return pdf_documents;
    
    def process_pdf_file(self, file_path: str) -> Optional[PDFDocument]:
        """处理单个 PDF 文件"""
        try:
            # 尝试使用 PyMuPDF
            if PYMUPDF_AVAILABLE:
                return self._process_with_pymupdf(file_path);
            # 回退到 PyPDF2
            elif PYPDF2_AVAILABLE:
                return self._process_with_pypdf2(file_path);
            else:
                print('没有可用的 PDF 处理库');
                return None;
                
        except Exception as e:
            print(f'处理 PDF 文件失败: {str(e)}');
            return None;
    
    def _process_with_pymupdf(self, file_path: str) -> Optional[PDFDocument]:
        """使用 PyMuPDF 处理 PDF"""
        try:
            doc = fitz.open(file_path);
            text_content = '';
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num);
                text_content += page.get_text();
            
            doc.close();
            
            # 清理文本
            cleaned_content = self._clean_text(text_content);
            
            # 提取标题（从文件名或内容）
            title = self._extract_title(file_path, cleaned_content);
            
            return PDFDocument(
                file_path=file_path,
                title=title,
                content=cleaned_content,
                page_count=len(doc),
                word_count=len(cleaned_content.split()),
                metadata={
                    'file_name': os.path.basename(file_path),
                    'file_size': os.path.getsize(file_path),
                    'extraction_method': 'PyMuPDF',
                },
            );
            
        except Exception as e:
            print(f'PyMuPDF 处理失败: {str(e)}');
            return None;
    
    def _process_with_pypdf2(self, file_path: str) -> Optional[PDFDocument]:
        """使用 PyPDF2 处理 PDF"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file);
                text_content = '';
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num];
                    text_content += page.extract_text();
            
            # 清理文本
            cleaned_content = self._clean_text(text_content);
            
            # 提取标题
            title = self._extract_title(file_path, cleaned_content);
            
            return PDFDocument(
                file_path=file_path,
                title=title,
                content=cleaned_content,
                page_count=len(pdf_reader.pages),
                word_count=len(cleaned_content.split()),
                metadata={
                    'file_name': os.path.basename(file_path),
                    'file_size': os.path.getsize(file_path),
                    'extraction_method': 'PyPDF2',
                },
            );
            
        except Exception as e:
            print(f'PyPDF2 处理失败: {str(e)}');
            return None;
    
    def _clean_text(self, text: str) -> str:
        """清理提取的文本"""
        cleaned = text;
        
        # 应用清理模式
        for pattern, replacement in self.text_cleanup_patterns:
            cleaned = re.sub(pattern, replacement, cleaned);
        
        # 移除过短的行
        lines = cleaned.split('\n');
        filtered_lines = [line.strip() for line in lines if len(line.strip()) > 3];
        
        return '\n'.join(filtered_lines).strip();
    
    def _extract_title(self, file_path: str, content: str) -> str:
        """从文件名或内容中提取标题"""
        # 首先尝试从文件名提取
        file_name = os.path.basename(file_path);
        name_without_ext = os.path.splitext(file_name)[0];
        
        # 移除数字前缀
        title = re.sub(r'^\d+_', '', name_without_ext);
        
        # 替换下划线为空格
        title = title.replace('_', ' ');
        
        # 如果标题太短，尝试从内容中提取
        if len(title) < 5:
            # 查找第一行非空内容作为标题
            lines = content.split('\n');
            for line in lines:
                line = line.strip();
                if len(line) > 10 and len(line) < 100:
                    title = line;
                    break;
        
        return title;
    
    def get_document_summary(self, documents: List[PDFDocument]) -> Dict[str, Any]:
        """获取文档集合的摘要信息"""
        if not documents:
            return {
                'total_documents': 0,
                'total_pages': 0,
                'total_words': 0,
                'avg_words_per_doc': 0,
                'document_types': {},
            };
        
        total_pages = sum(doc.page_count for doc in documents);
        total_words = sum(doc.word_count for doc in documents);
        
        # 分析文档类型
        doc_types = {};
        for doc in documents:
            file_name = os.path.basename(doc.file_path);
            # 根据文件名分类
            if 'NGF' in file_name.upper():
                doc_types['NGF相关'] = doc_types.get('NGF相关', 0) + 1;
            elif 'mRNA' in file_name.upper():
                doc_types['mRNA相关'] = doc_types.get('mRNA相关', 0) + 1;
            elif 'RNA' in file_name.upper():
                doc_types['RNA相关'] = doc_types.get('RNA相关', 0) + 1;
            else:
                doc_types['其他'] = doc_types.get('其他', 0) + 1;
        
        return {
            'total_documents': len(documents),
            'total_pages': total_pages,
            'total_words': total_words,
            'avg_words_per_doc': total_words / len(documents) if documents else 0,
            'document_types': doc_types,
        };


# 使用示例
if __name__ == '__main__':
    processor = PDFProcessor();
    
    # 处理 PDF 目录
    pdf_directory = './data/related_paper';
    documents = processor.process_pdf_directory(pdf_directory);
    
    print(f'\n处理完成，共处理 {len(documents)} 个文档');
    
    # 显示摘要
    summary = processor.get_document_summary(documents);
    print(f'\n文档摘要:');
    print(f'- 总文档数: {summary["total_documents"]}');
    print(f'- 总页数: {summary["total_pages"]}');
    print(f'- 总字数: {summary["total_words"]}');
    print(f'- 平均每文档字数: {summary["avg_words_per_doc"]:.0f}');
    print(f'- 文档类型分布: {summary["document_types"]}');
    
    # 显示前几个文档的信息
    print(f'\n前 5 个文档:');
    for i, doc in enumerate(documents[:5], 1):
        print(f'{i}. {doc.title}');
        print(f'   文件: {doc.metadata["file_name"]}');
        print(f'   页数: {doc.page_count}, 字数: {doc.word_count}');
        print(f'   内容预览: {doc.content[:100]}...');
        print();
